# 瓜田 (Guatian) - Story Sharing Platform

A Flutter-based story sharing platform with voting and gamification features. Users can share stories, vote on different camps, express beliefs, and earn XP and medals.

## Features

### MVP Features
- **User System**: Automatic anonymous account creation
- **Story Posting**: Create and publish stories with camp voting
- **Story Feed**: Browse stories in the main "瓜田" feed
- **Voting System**:
  - Camp voting (站队支持)
  - Belief voting (信/不信)
  - Emoji reactions
- **Heat Calculation**: Backend calculation of story popularity
- **Level System**: XP gain and automatic level progression
- **Medal System**: Achievement badges with automatic awarding

### Design Features
- **Dark Theme**: Modern dark mode with custom color palette
- **Custom Animations**: Running "猹" loading animation, micro-interactions
- **Typography**: Montserrat for titles, Noto Sans SC for body text
- **Cultural Elements**: Watermelon (🍉) theme and Chinese internet culture references

## Tech Stack

- **Frontend**: Flutter with Riverpod for state management
- **Backend**: Supabase (PostgreSQL + Edge Functions)
- **Authentication**: Supabase Auth (Anonymous users)
- **Real-time**: Supabase real-time subscriptions

## Project Structure

```
lib/
├── main.dart
├── core/
│   ├── config/
│   │   └── supabase_config.dart
│   ├── theme/
│   │   └── app_theme.dart
│   ├── router/
│   │   └── app_router.dart
│   ├── models/
│   │   ├── user.dart
│   │   ├── story.dart
│   │   ├── vote.dart
│   │   ├── reaction.dart
│   │   └── medal.dart
│   └── services/
│       └── story_service.dart
├── features/
│   ├── auth/
│   │   ├── pages/
│   │   └── providers/
│   ├── feed/
│   │   ├── pages/
│   │   ├── widgets/
│   │   └── providers/
│   ├── story/
│   │   ├── pages/
│   │   ├── widgets/
│   │   └── providers/
│   ├── profile/
│   │   ├── pages/
│   │   ├── widgets/
│   │   └── providers/
│   └── shared/
│       └── widgets/
```

## Setup Instructions

### Prerequisites
- Flutter SDK (>=3.0.0)
- Dart SDK
- Supabase account

### 1. Clone and Install Dependencies
```bash
git clone <repository-url>
cd guatian
flutter pub get
```

### 2. Supabase Setup
1. Create a new Supabase project
2. Set up the database tables (see Database Schema below)
3. Create Edge Functions for heat calculation and medal awarding
4. Update `lib/core/config/supabase_config.dart` with your project credentials

### 3. Database Schema
Run these SQL commands in your Supabase SQL editor:

```sql
-- Users table
CREATE TABLE users (
  id UUID PRIMARY KEY REFERENCES auth.users(id),
  level INTEGER DEFAULT 1,
  xp INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Stories table
CREATE TABLE stories (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  author_id UUID REFERENCES users(id),
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  section TEXT NOT NULL,
  tags TEXT[] DEFAULT '{}',
  camp_a_name TEXT NOT NULL,
  camp_b_name TEXT NOT NULL,
  heat INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Votes table
CREATE TABLE votes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  story_id UUID REFERENCES stories(id),
  user_id UUID REFERENCES users(id),
  vote_type TEXT NOT NULL,
  vote_value TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(story_id, user_id, vote_type)
);

-- Reactions table
CREATE TABLE reactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  story_id UUID REFERENCES stories(id),
  user_id UUID REFERENCES users(id),
  emoji_text TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(story_id, user_id, emoji_text)
);

-- Medals table
CREATE TABLE medals (
  id SERIAL PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT NOT NULL,
  icon_url TEXT NOT NULL
);

-- User medals table
CREATE TABLE user_medals (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  medal_id INTEGER REFERENCES medals(id),
  achieved_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id, medal_id)
);
```

### 4. Insert Initial Medal Data
```sql
INSERT INTO medals (name, description, icon_url) VALUES
('第一口瓜', '发布了第一个故事', 'assets/icons/first_melon.png'),
('勤劳的猹', '连续7天发布故事', 'assets/icons/hardworking_zha.png'),
('瓜田守护者', '获得100个赞', 'assets/icons/guardian.png'),
('热瓜制造机', '发布的故事总热度达到1000', 'assets/icons/hot_maker.png'),
('吃瓜群众', '对100个故事进行了投票', 'assets/icons/voter.png'),
('表情包大师', '使用了所有类型的emoji反应', 'assets/icons/emoji_master.png'),
('瓜田新人', '注册账号', 'assets/icons/newcomer.png'),
('等级达人', '达到10级', 'assets/icons/level_master.png');
```

### 5. Run the App
```bash
flutter run
```

## Development Status

This project structure has been created with all the core components needed for the MVP. The following files need to be implemented:

- Story detail voting widgets (CampVoteBar, BeliefVoteSection, EmojiReactionBar)
- Profile widgets (MedalWall, XPProgressBar)
- Providers for story detail and profile
- Edge Functions for heat calculation and medal awarding
- Model code generation (run `flutter packages pub run build_runner build`)

## Contributing

Please refer to the documentation in `/docs/` for detailed requirements:
- `mvp.md` - MVP feature requirements
- `design.md` - Design language and visual style
- `frontend.md` - Flutter UI component specifications
- `backend.md` - Supabase database structure

## License

[Add your license here]
