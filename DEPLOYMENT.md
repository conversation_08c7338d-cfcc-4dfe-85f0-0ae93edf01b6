# 瓜田 (Guatian) - Deployment Guide

This guide covers the deployment process for the Guatian Flutter app.

## Prerequisites

- Flutter SDK (>=3.0.0)
- Supabase account and project
- Android Studio / Xcode for mobile deployment
- Web hosting service (for web deployment)

## Environment Setup

### 1. Environment Variables

Create a `.env` file in the project root:

```env
SUPABASE_URL=https://rrxsdllelhdnbatmvact.supabase.co
SUPABASE_ANON_KEY=your_actual_anon_key_here
```

### 2. Supabase Configuration

#### Database Setup

1. Run the migration files in order:
   ```sql
   -- Execute in Supabase SQL Editor
   -- 1. supabase/migrations/001_initial_schema.sql
   -- 2. supabase/migrations/002_insert_medals.sql
   ```

2. Enable Row Level Security (RLS) on all tables
3. Configure authentication settings:
   - Enable anonymous sign-ins
   - Set session timeout as needed

#### Edge Functions Deployment

Deploy the Edge Functions using Supabase CLI:

```bash
# Install Supabase CLI
npm install -g supabase

# Login to Supabase
supabase login

# Link to your project
supabase link --project-ref your-project-ref

# Deploy functions
supabase functions deploy calculate-heat
supabase functions deploy award-medals
```

## Build Process

### 1. Code Generation

```bash
# Generate model files
flutter packages pub run build_runner build --delete-conflicting-outputs
```

### 2. Testing

```bash
# Run unit tests
flutter test

# Run integration tests
flutter test integration_test/

# Run widget tests
flutter test test/widgets/
```

### 3. Code Quality

```bash
# Analyze code
flutter analyze

# Format code
flutter format .
```

## Platform-Specific Deployment

### Android

1. **Configure signing**:
   - Create `android/key.properties`
   - Generate keystore file
   - Update `android/app/build.gradle`

2. **Build APK/AAB**:
   ```bash
   # Debug APK
   flutter build apk --debug
   
   # Release APK
   flutter build apk --release
   
   # App Bundle (recommended for Play Store)
   flutter build appbundle --release
   ```

3. **Deploy to Play Store**:
   - Upload AAB file to Play Console
   - Configure store listing
   - Submit for review

### iOS

1. **Configure signing**:
   - Set up Apple Developer account
   - Configure provisioning profiles
   - Update `ios/Runner.xcodeproj`

2. **Build IPA**:
   ```bash
   # Release build
   flutter build ios --release
   
   # Archive in Xcode
   open ios/Runner.xcworkspace
   ```

3. **Deploy to App Store**:
   - Archive and upload via Xcode
   - Configure App Store Connect
   - Submit for review

### Web

1. **Build for web**:
   ```bash
   flutter build web --release
   ```

2. **Deploy to hosting service**:
   ```bash
   # Example: Firebase Hosting
   firebase deploy --only hosting
   
   # Example: Netlify
   netlify deploy --prod --dir=build/web
   
   # Example: Vercel
   vercel --prod build/web
   ```

## Performance Optimization

### 1. Build Optimizations

```bash
# Enable tree shaking and minification
flutter build apk --release --shrink

# Optimize for size
flutter build apk --release --split-per-abi
```

### 2. Asset Optimization

- Compress images using tools like `flutter_launcher_icons`
- Use vector graphics (SVG) where possible
- Implement lazy loading for large assets

### 3. Code Splitting

- Use deferred imports for large features
- Implement route-based code splitting
- Optimize bundle size with `--analyze-size`

## Monitoring and Analytics

### 1. Crash Reporting

Consider integrating:
- Firebase Crashlytics
- Sentry
- Bugsnag

### 2. Performance Monitoring

- Firebase Performance Monitoring
- Custom performance metrics
- User analytics

### 3. Error Tracking

The app includes built-in error handling:
- `ErrorHandler` class for consistent error messages
- `ErrorBoundary` widget for graceful error recovery
- Performance monitoring with `PerformanceUtils`

## Security Considerations

### 1. API Keys

- Never commit API keys to version control
- Use environment variables
- Implement key rotation strategy

### 2. Data Protection

- Enable RLS on all Supabase tables
- Implement proper authentication flows
- Validate all user inputs

### 3. Network Security

- Use HTTPS for all API calls
- Implement certificate pinning
- Add request/response encryption if needed

## Maintenance

### 1. Regular Updates

- Keep Flutter SDK updated
- Update dependencies regularly
- Monitor security advisories

### 2. Database Maintenance

- Regular backups
- Performance monitoring
- Index optimization

### 3. User Feedback

- Implement in-app feedback
- Monitor app store reviews
- Track user analytics

## Troubleshooting

### Common Issues

1. **Build failures**:
   - Clean build: `flutter clean && flutter pub get`
   - Check Flutter doctor: `flutter doctor`
   - Verify environment variables

2. **Supabase connection issues**:
   - Verify URL and API keys
   - Check network connectivity
   - Review RLS policies

3. **Performance issues**:
   - Use Flutter Inspector
   - Profile with `flutter run --profile`
   - Monitor memory usage

### Debug Commands

```bash
# Debug build with logging
flutter run --debug --verbose

# Profile performance
flutter run --profile

# Analyze bundle size
flutter build apk --analyze-size
```

## Support

For deployment issues:
1. Check Flutter documentation
2. Review Supabase documentation
3. Check project GitHub issues
4. Contact development team

---

**Note**: This deployment guide assumes you have the necessary accounts and permissions for the target platforms. Always test deployments in staging environments before production releases.
