import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class ErrorHandler {
  static String getErrorMessage(dynamic error) {
    if (error is PostgrestException) {
      return _handlePostgrestError(error);
    } else if (error is AuthException) {
      return _handleAuthError(error);
    } else if (error is StorageException) {
      return _handleStorageError(error);
    } else if (error is FunctionException) {
      return _handleFunctionError(error);
    } else {
      return _handleGenericError(error);
    }
  }

  static String _handlePostgrestError(PostgrestException error) {
    switch (error.code) {
      case '23505': // Unique violation
        return '数据已存在，请检查后重试';
      case '23503': // Foreign key violation
        return '数据关联错误，请联系管理员';
      case '42501': // Insufficient privilege
        return '权限不足，无法执行此操作';
      case 'PGRST116': // No rows found
        return '未找到相关数据';
      default:
        if (kDebugMode) {
          print('Postgrest error: ${error.code} - ${error.message}');
        }
        return '数据库操作失败，请稍后重试';
    }
  }

  static String _handleAuthError(AuthException error) {
    switch (error.message) {
      case 'Invalid login credentials':
        return '登录凭据无效';
      case 'Email not confirmed':
        return '邮箱未验证';
      case 'User not found':
        return '用户不存在';
      case 'Password is too weak':
        return '密码强度不够';
      default:
        if (kDebugMode) {
          print('Auth error: ${error.message}');
        }
        return '认证失败，请重新登录';
    }
  }

  static String _handleStorageError(StorageException error) {
    if (kDebugMode) {
      print('Storage error: ${error.message}');
    }
    return '文件操作失败，请稍后重试';
  }

  static String _handleFunctionError(FunctionException error) {
    if (kDebugMode) {
      print('Function error: ${error.details}');
    }
    return '服务器处理失败，请稍后重试';
  }

  static String _handleGenericError(dynamic error) {
    if (kDebugMode) {
      print('Generic error: $error');
    }
    
    final errorString = error.toString().toLowerCase();
    
    if (errorString.contains('network') || errorString.contains('connection')) {
      return '网络连接失败，请检查网络设置';
    } else if (errorString.contains('timeout')) {
      return '请求超时，请稍后重试';
    } else if (errorString.contains('format')) {
      return '数据格式错误';
    } else {
      return '操作失败，请稍后重试';
    }
  }

  static void logError(dynamic error, StackTrace? stackTrace, {String? context}) {
    if (kDebugMode) {
      print('=== ERROR LOG ===');
      if (context != null) {
        print('Context: $context');
      }
      print('Error: $error');
      if (stackTrace != null) {
        print('Stack trace: $stackTrace');
      }
      print('================');
    }
    
    // In production, you might want to send this to a crash reporting service
    // like Firebase Crashlytics or Sentry
  }
}

class AppException implements Exception {
  final String message;
  final String? code;
  final dynamic originalError;

  const AppException(
    this.message, {
    this.code,
    this.originalError,
  });

  @override
  String toString() => message;
}

class NetworkException extends AppException {
  const NetworkException(String message) : super(message);
}

class ValidationException extends AppException {
  const ValidationException(String message) : super(message);
}

class AuthenticationException extends AppException {
  const AuthenticationException(String message) : super(message);
}

class PermissionException extends AppException {
  const PermissionException(String message) : super(message);
}
