import 'package:json_annotation/json_annotation.dart';

part 'vote.g.dart';

enum VoteType {
  @JsonValue('camp')
  camp,
  @JsonValue('belief')
  belief,
}

enum VoteValue {
  // Camp votes
  @JsonValue('camp_a')
  campA,
  @JsonValue('camp_b')
  campB,
  
  // Belief votes
  @JsonValue('believe')
  believe,
  @JsonValue('unbelieve_exaggerated')
  unbelieveExaggerated,
  @JsonValue('unbelieve_fake')
  unbelieveFake,
  @JsonValue('unbelieve_incomplete')
  unbelieveIncomplete,
  @JsonValue('unbelieve_biased')
  unbelieveBiased,
}

@JsonSerializable()
class Vote {
  final String id;
  @JsonKey(name: 'story_id')
  final String storyId;
  @JsonKey(name: 'user_id')
  final String userId;
  @JsonKey(name: 'vote_type')
  final VoteType voteType;
  @<PERSON><PERSON><PERSON>ey(name: 'vote_value')
  final VoteValue voteValue;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'created_at')
  final DateTime? createdAt;

  const Vote({
    required this.id,
    required this.storyId,
    required this.userId,
    required this.voteType,
    required this.voteValue,
    this.createdAt,
  });

  factory Vote.fromJson(Map<String, dynamic> json) => _$VoteFromJson(json);
  Map<String, dynamic> toJson() => _$VoteToJson(this);

  Vote copyWith({
    String? id,
    String? storyId,
    String? userId,
    VoteType? voteType,
    VoteValue? voteValue,
    DateTime? createdAt,
  }) {
    return Vote(
      id: id ?? this.id,
      storyId: storyId ?? this.storyId,
      userId: userId ?? this.userId,
      voteType: voteType ?? this.voteType,
      voteValue: voteValue ?? this.voteValue,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Vote &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Vote{id: $id, storyId: $storyId, voteType: $voteType, voteValue: $voteValue}';
  }
}

// Helper extensions for vote values
extension VoteValueExtension on VoteValue {
  String get displayName {
    switch (this) {
      case VoteValue.campA:
        return '阵营A';
      case VoteValue.campB:
        return '阵营B';
      case VoteValue.believe:
        return '信';
      case VoteValue.unbelieveExaggerated:
        return '不信 - 夸大了';
      case VoteValue.unbelieveFake:
        return '不信 - 编的';
      case VoteValue.unbelieveIncomplete:
        return '不信 - 不完整';
      case VoteValue.unbelieveBiased:
        return '不信 - 有偏见';
    }
  }

  bool get isBelief {
    return this == VoteValue.believe;
  }

  bool get isUnbelief {
    return [
      VoteValue.unbelieveExaggerated,
      VoteValue.unbelieveFake,
      VoteValue.unbelieveIncomplete,
      VoteValue.unbelieveBiased,
    ].contains(this);
  }

  bool get isCamp {
    return [VoteValue.campA, VoteValue.campB].contains(this);
  }
}
