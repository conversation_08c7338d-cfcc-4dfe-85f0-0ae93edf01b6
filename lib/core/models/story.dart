import 'package:json_annotation/json_annotation.dart';

part 'story.g.dart';

@JsonSerializable()
class Story {
  final String id;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'author_id')
  final String authorId;
  final String title;
  final String content;
  final String section;
  final List<String> tags;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'camp_a_name')
  final String campAName;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'camp_b_name')
  final String campBName;
  final int heat;
  @Json<PERSON>ey(name: 'created_at')
  final DateTime createdAt;

  // Additional fields for UI (not stored in DB)
  final int? campAVotes;
  final int? campBVotes;
  final int? believeVotes;
  final int? unbelieveVotes;
  final Map<String, int>? emojiReactions;
  final int? authorLevel;

  const Story({
    required this.id,
    required this.authorId,
    required this.title,
    required this.content,
    required this.section,
    required this.tags,
    required this.campAName,
    required this.campBName,
    required this.heat,
    required this.createdAt,
    this.campAVotes,
    this.campBVotes,
    this.believeVotes,
    this.unbelieveVotes,
    this.emojiReactions,
    this.authorLevel,
  });

  factory Story.fromJson(Map<String, dynamic> json) => _$StoryFromJson(json);
  Map<String, dynamic> toJson() => _$StoryToJson(this);

  Story copyWith({
    String? id,
    String? authorId,
    String? title,
    String? content,
    String? section,
    List<String>? tags,
    String? campAName,
    String? campBName,
    int? heat,
    DateTime? createdAt,
    int? campAVotes,
    int? campBVotes,
    int? believeVotes,
    int? unbelieveVotes,
    Map<String, int>? emojiReactions,
    int? authorLevel,
  }) {
    return Story(
      id: id ?? this.id,
      authorId: authorId ?? this.authorId,
      title: title ?? this.title,
      content: content ?? this.content,
      section: section ?? this.section,
      tags: tags ?? this.tags,
      campAName: campAName ?? this.campAName,
      campBName: campBName ?? this.campBName,
      heat: heat ?? this.heat,
      createdAt: createdAt ?? this.createdAt,
      campAVotes: campAVotes ?? this.campAVotes,
      campBVotes: campBVotes ?? this.campBVotes,
      believeVotes: believeVotes ?? this.believeVotes,
      unbelieveVotes: unbelieveVotes ?? this.unbelieveVotes,
      emojiReactions: emojiReactions ?? this.emojiReactions,
      authorLevel: authorLevel ?? this.authorLevel,
    );
  }

  // Calculate camp vote percentages
  double get campAPercentage {
    final totalVotes = (campAVotes ?? 0) + (campBVotes ?? 0);
    if (totalVotes == 0) return 0.5;
    return (campAVotes ?? 0) / totalVotes;
  }

  double get campBPercentage {
    return 1.0 - campAPercentage;
  }

  // Get content preview for feed
  String get contentPreview {
    if (content.length <= 100) return content;
    return '${content.substring(0, 100)}...';
  }

  // Get total interaction count
  int get totalInteractions {
    final votes = (campAVotes ?? 0) + (campBVotes ?? 0) + (believeVotes ?? 0) + (unbelieveVotes ?? 0);
    final emojis = emojiReactions?.values.fold(0, (sum, count) => sum + count) ?? 0;
    return votes + emojis;
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Story &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Story{id: $id, title: $title, heat: $heat}';
  }
}
