import 'package:json_annotation/json_annotation.dart';

part 'reaction.g.dart';

@JsonSerializable()
class Reaction {
  final String id;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'story_id')
  final String storyId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'user_id')
  final String userId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'emoji_text')
  final String emojiText;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'created_at')
  final DateTime? createdAt;

  const Reaction({
    required this.id,
    required this.storyId,
    required this.userId,
    required this.emojiText,
    this.createdAt,
  });

  factory Reaction.fromJson(Map<String, dynamic> json) => _$ReactionFromJson(json);
  Map<String, dynamic> toJson() => _$ReactionToJson(this);

  Reaction copyWith({
    String? id,
    String? storyId,
    String? userId,
    String? emojiText,
    DateTime? createdAt,
  }) {
    return Reaction(
      id: id ?? this.id,
      storyId: storyId ?? this.storyId,
      userId: userId ?? this.userId,
      emojiText: emojiText ?? this.emojiText,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Reaction &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Reaction{id: $id, storyId: $storyId, emojiText: $emojiText}';
  }
}

// Common emoji reactions
class CommonEmojis {
  static const List<String> reactions = [
    '😂', // 笑哭
    '😱', // 震惊
    '🤔', // 思考
    '👍', // 赞
    '👎', // 踩
    '❤️', // 爱心
    '😡', // 愤怒
    '🤯', // 爆炸头
    '🙄', // 翻白眼
    '😭', // 大哭
    '🔥', // 火
    '💯', // 100分
    '🤡', // 小丑
    '👀', // 眼睛
    '🍉', // 瓜（主题相关）
  ];

  static String getEmojiName(String emoji) {
    switch (emoji) {
      case '😂':
        return '笑哭';
      case '😱':
        return '震惊';
      case '🤔':
        return '思考';
      case '👍':
        return '赞';
      case '👎':
        return '踩';
      case '❤️':
        return '爱心';
      case '😡':
        return '愤怒';
      case '🤯':
        return '爆炸头';
      case '🙄':
        return '翻白眼';
      case '😭':
        return '大哭';
      case '🔥':
        return '火';
      case '💯':
        return '100分';
      case '🤡':
        return '小丑';
      case '👀':
        return '眼睛';
      case '🍉':
        return '瓜';
      default:
        return emoji;
    }
  }
}
