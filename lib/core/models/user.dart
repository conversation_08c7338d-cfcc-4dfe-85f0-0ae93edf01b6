import 'package:json_annotation/json_annotation.dart';

part 'user.g.dart';

@JsonSerializable()
class User {
  final String id;
  final int level;
  final int xp;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;

  const User({
    required this.id,
    required this.level,
    required this.xp,
    required this.createdAt,
  });

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
  Map<String, dynamic> toJson() => _$UserToJson(this);

  User copyWith({
    String? id,
    int? level,
    int? xp,
    DateTime? createdAt,
  }) {
    return User(
      id: id ?? this.id,
      level: level ?? this.level,
      xp: xp ?? this.xp,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  // Calculate XP needed for next level
  int get xpForNextLevel {
    return level * 100; // Simple formula: level * 100 XP needed
  }

  // Calculate XP progress for current level
  double get levelProgress {
    final currentLevelXp = (level - 1) * 100;
    final nextLevelXp = level * 100;
    final progressXp = xp - currentLevelXp;
    final totalXpNeeded = nextLevelXp - currentLevelXp;
    return progressXp / totalXpNeeded;
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is User &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          level == other.level &&
          xp == other.xp &&
          createdAt == other.createdAt;

  @override
  int get hashCode => Object.hash(id, level, xp, createdAt);

  @override
  String toString() {
    return 'User{id: $id, level: $level, xp: $xp, createdAt: $createdAt}';
  }
}
