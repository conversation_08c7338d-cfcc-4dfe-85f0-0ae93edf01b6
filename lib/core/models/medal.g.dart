// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'medal.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Medal _$MedalFromJson(Map<String, dynamic> json) => Medal(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      description: json['description'] as String,
      iconUrl: json['icon_url'] as String,
      medalType: json['medal_type'] as String,
    );

Map<String, dynamic> _$MedalToJson(Medal instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'icon_url': instance.iconUrl,
      'medal_type': instance.medalType,
    };

UserMedal _$UserMedalFromJson(Map<String, dynamic> json) => UserMedal(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      medalId: (json['medal_id'] as num).toInt(),
      achievedAt: DateTime.parse(json['achieved_at'] as String),
      medal: json['medal'] == null
          ? null
          : Medal.fromJson(json['medal'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$UserMedalToJson(UserMedal instance) => <String, dynamic>{
      'id': instance.id,
      'user_id': instance.userId,
      'medal_id': instance.medalId,
      'achieved_at': instance.achievedAt.toIso8601String(),
      'medal': instance.medal,
    };
