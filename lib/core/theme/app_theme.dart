import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class AppColors {
  // Background colors
  static const Color primaryBackground = Color(0xFF1A1A1A);
  static const Color cardBackground = Color(0xFF252525);
  
  // Accent colors
  static const Color guatianGreen = Color(0xFF00C853);
  static const Color hotMelonYellow = Color(0xFFFFD600);
  
  // Text colors
  static const Color primaryText = Color(0xFFFFFFFF);
  static const Color bodyText = Color(0xFFE0E0E0);
  static const Color secondaryText = Color(0xFF888888);
  
  // Additional colors
  static const Color error = Color(0xFFFF5252);
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
}

class AppTheme {
  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      scaffoldBackgroundColor: AppColors.primaryBackground,
      
      // Color scheme
      colorScheme: const ColorScheme.dark(
        primary: AppColors.guatianGreen,
        secondary: AppColors.hotMelonYellow,
        surface: AppColors.cardBackground,
        onPrimary: Colors.black,
        onSecondary: Colors.black,
        onSurface: AppColors.bodyText,
      ),
      
      // Typography
      textTheme: TextTheme(
        // Headlines use Montserrat
        headlineLarge: GoogleFonts.montserrat(
          fontSize: 32,
          fontWeight: FontWeight.w800,
          color: AppColors.primaryText,
        ),
        headlineMedium: GoogleFonts.montserrat(
          fontSize: 28,
          fontWeight: FontWeight.w700,
          color: AppColors.primaryText,
        ),
        headlineSmall: GoogleFonts.montserrat(
          fontSize: 24,
          fontWeight: FontWeight.w700,
          color: AppColors.primaryText,
        ),
        
        // Titles use Montserrat
        titleLarge: GoogleFonts.montserrat(
          fontSize: 22,
          fontWeight: FontWeight.w700,
          color: AppColors.primaryText,
        ),
        titleMedium: GoogleFonts.montserrat(
          fontSize: 16,
          fontWeight: FontWeight.w700,
          color: AppColors.primaryText,
        ),
        titleSmall: GoogleFonts.montserrat(
          fontSize: 14,
          fontWeight: FontWeight.w700,
          color: AppColors.primaryText,
        ),
        
        // Body text uses Noto Sans SC
        bodyLarge: GoogleFonts.notoSansSc(
          fontSize: 16,
          color: AppColors.bodyText,
        ),
        bodyMedium: GoogleFonts.notoSansSc(
          fontSize: 14,
          color: AppColors.bodyText,
        ),
        bodySmall: GoogleFonts.notoSansSc(
          fontSize: 12,
          color: AppColors.secondaryText,
        ),
        
        // Labels use Noto Sans SC
        labelLarge: GoogleFonts.notoSansSc(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: AppColors.bodyText,
        ),
        labelMedium: GoogleFonts.notoSansSc(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          color: AppColors.bodyText,
        ),
        labelSmall: GoogleFonts.notoSansSc(
          fontSize: 11,
          fontWeight: FontWeight.w500,
          color: AppColors.secondaryText,
        ),
      ),
      
      // App bar theme
      appBarTheme: AppBarTheme(
        backgroundColor: AppColors.primaryBackground,
        foregroundColor: AppColors.primaryText,
        elevation: 0,
        titleTextStyle: GoogleFonts.montserrat(
          fontSize: 20,
          fontWeight: FontWeight.w700,
          color: AppColors.primaryText,
        ),
      ),
      
      // Card theme
      cardTheme: const CardThemeData(
        color: AppColors.cardBackground,
        elevation: 2,
        margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      ),
      
      // Button themes
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.guatianGreen,
          foregroundColor: Colors.black,
          textStyle: GoogleFonts.montserrat(
            fontWeight: FontWeight.w700,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
      
      // Input decoration theme
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: AppColors.cardBackground,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide.none,
        ),
        hintStyle: GoogleFonts.notoSansSc(
          color: AppColors.secondaryText,
        ),
      ),
    );
  }
}
