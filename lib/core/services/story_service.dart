import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/story.dart';
import '../models/vote.dart';
import '../utils/error_handler.dart';
import '../utils/performance_utils.dart';

class StoryService {
  final SupabaseClient _supabase;

  StoryService(this._supabase);

  // Get stories for feed
  Future<List<Story>> getStories({
    int offset = 0,
    int limit = 20,
    String? section,
  }) async {
    try {
      return await PerformanceUtils.measureAsync(
        'getStories(offset: $offset, limit: $limit)',
        () async {
          // Check cache first
          final cacheKey = 'stories_${offset}_${limit}_$section';
          final cached = PerformanceUtils.getCached<List<Story>>(cacheKey);
          if (cached != null) {
            return cached;
          }

          dynamic query = _supabase
              .from('stories')
              .select('''
                *,
                users!stories_author_id_fkey(level),
                votes(vote_type, vote_value),
                reactions(emoji_text)
              ''');

          if (section != null) {
            query = query.eq('section', section);
          }

          query = query
              .order('heat', ascending: false)
              .order('created_at', ascending: false)
              .range(offset, offset + limit - 1);

          final response = await query;
    
          final stories = response.map<Story>((data) {
            // Process votes and reactions
            final votes = data['votes'] as List<dynamic>? ?? [];
            final reactions = data['reactions'] as List<dynamic>? ?? [];
            final user = data['users'] as Map<String, dynamic>?;

            // Count votes by type
            int campAVotes = 0;
            int campBVotes = 0;
            int believeVotes = 0;
            int unbelieveVotes = 0;

            for (final vote in votes) {
              final voteValue = vote['vote_value'] as String;
              switch (voteValue) {
                case 'camp_a':
                  campAVotes++;
                  break;
                case 'camp_b':
                  campBVotes++;
                  break;
                case 'believe':
                  believeVotes++;
                  break;
                default:
                  if (voteValue.startsWith('unbelieve_')) {
                    unbelieveVotes++;
                  }
                  break;
              }
            }

            // Count emoji reactions
            final emojiCounts = <String, int>{};
            for (final reaction in reactions) {
              final emoji = reaction['emoji_text'] as String;
              emojiCounts[emoji] = (emojiCounts[emoji] ?? 0) + 1;
            }

            return Story.fromJson({
              ...data,
              'campAVotes': campAVotes,
              'campBVotes': campBVotes,
              'believeVotes': believeVotes,
              'unbelieveVotes': unbelieveVotes,
              'emojiReactions': emojiCounts,
              'authorLevel': user?['level'] ?? 1,
            });
          }).toList();

          // Cache the result
          PerformanceUtils.setCached(cacheKey, stories, ttl: const Duration(minutes: 2));

          return stories;
        },
      );
    } catch (error, stackTrace) {
      ErrorHandler.logError(error, stackTrace, context: 'getStories');
      throw AppException(ErrorHandler.getErrorMessage(error));
    }
  }

  // Get single story with details
  Future<Story?> getStory(String storyId) async {
    final response = await _supabase
        .from('stories')
        .select('''
          *,
          users!stories_author_id_fkey(level),
          votes(vote_type, vote_value),
          reactions(emoji_text)
        ''')
        .eq('id', storyId)
        .single();

    // Response is never null from Supabase single() call

    // Process votes and reactions (same logic as above)
    final votes = response['votes'] as List<dynamic>? ?? [];
    final reactions = response['reactions'] as List<dynamic>? ?? [];
    final user = response['users'] as Map<String, dynamic>?;

    int campAVotes = 0;
    int campBVotes = 0;
    int believeVotes = 0;
    int unbelieveVotes = 0;

    for (final vote in votes) {
      final voteValue = vote['vote_value'] as String;
      switch (voteValue) {
        case 'camp_a':
          campAVotes++;
          break;
        case 'camp_b':
          campBVotes++;
          break;
        case 'believe':
          believeVotes++;
          break;
        default:
          if (voteValue.startsWith('unbelieve_')) {
            unbelieveVotes++;
          }
          break;
      }
    }

    final emojiCounts = <String, int>{};
    for (final reaction in reactions) {
      final emoji = reaction['emoji_text'] as String;
      emojiCounts[emoji] = (emojiCounts[emoji] ?? 0) + 1;
    }

    return Story.fromJson({
      ...response,
      'campAVotes': campAVotes,
      'campBVotes': campBVotes,
      'believeVotes': believeVotes,
      'unbelieveVotes': unbelieveVotes,
      'emojiReactions': emojiCounts,
      'authorLevel': user?['level'] ?? 1,
    });
  }

  // Create new story
  Future<Story> createStory({
    required String title,
    required String content,
    required String section,
    required List<String> tags,
    required String campAName,
    required String campBName,
  }) async {
    final user = _supabase.auth.currentUser;
    if (user == null) throw Exception('User not authenticated');

    final response = await _supabase
        .from('stories')
        .insert({
          'author_id': user.id,
          'title': title,
          'content': content,
          'section': section,
          'tags': tags,
          'camp_a_name': campAName,
          'camp_b_name': campBName,
          'heat': 0,
        })
        .select()
        .single();

    return Story.fromJson(response);
  }

  // Vote on story
  Future<void> vote({
    required String storyId,
    required VoteType voteType,
    required VoteValue voteValue,
  }) async {
    final user = _supabase.auth.currentUser;
    if (user == null) throw Exception('User not authenticated');

    // Check if user already voted on this story with this vote type
    final existingVote = await _supabase
        .from('votes')
        .select()
        .eq('story_id', storyId)
        .eq('user_id', user.id)
        .eq('vote_type', voteType.name)
        .maybeSingle();

    if (existingVote != null) {
      // Update existing vote
      await _supabase
          .from('votes')
          .update({'vote_value': voteValue.name})
          .eq('id', existingVote['id']);
    } else {
      // Create new vote
      await _supabase.from('votes').insert({
        'story_id': storyId,
        'user_id': user.id,
        'vote_type': voteType.name,
        'vote_value': voteValue.name,
      });
    }

    // Trigger heat calculation
    await _triggerHeatCalculation(storyId);
  }

  // Add emoji reaction
  Future<void> addReaction({
    required String storyId,
    required String emoji,
  }) async {
    final user = _supabase.auth.currentUser;
    if (user == null) throw Exception('User not authenticated');

    // Check if user already reacted with this emoji
    final existingReaction = await _supabase
        .from('reactions')
        .select()
        .eq('story_id', storyId)
        .eq('user_id', user.id)
        .eq('emoji_text', emoji)
        .maybeSingle();

    if (existingReaction == null) {
      // Add new reaction
      await _supabase.from('reactions').insert({
        'story_id': storyId,
        'user_id': user.id,
        'emoji_text': emoji,
      });

      // Trigger heat calculation
      await _triggerHeatCalculation(storyId);
    }
  }

  // Remove emoji reaction
  Future<void> removeReaction({
    required String storyId,
    required String emoji,
  }) async {
    final user = _supabase.auth.currentUser;
    if (user == null) throw Exception('User not authenticated');

    await _supabase
        .from('reactions')
        .delete()
        .eq('story_id', storyId)
        .eq('user_id', user.id)
        .eq('emoji_text', emoji);

    // Trigger heat calculation
    await _triggerHeatCalculation(storyId);
  }

  // Trigger heat calculation Edge Function
  Future<void> _triggerHeatCalculation(String storyId) async {
    try {
      await _supabase.functions.invoke(
        'calculate-heat',
        body: {'story_id': storyId},
      );
    } catch (e) {
      // Log error but don't throw - heat calculation is not critical for UX
      print('Heat calculation failed: $e');
    }
  }
}

final storyServiceProvider = Provider<StoryService>((ref) {
  return StoryService(Supabase.instance.client);
});
