import 'package:flutter_dotenv/flutter_dotenv.dart';

class SupabaseConfig {
  static String get url => dotenv.env['SUPABASE_URL'] ?? '';
  static String get anonKey => dotenv.env['SUPABASE_ANON_KEY'] ?? '';

  // Edge Function URLs
  static String get calculateHeatFunction => '$url/functions/v1/calculate-heat';
  static String get awardMedalsFunction => '$url/functions/v1/award-medals';

  // Heat calculation weights based on product.md
  static const Map<int, double> levelWeights = {
    1: 1.0,   // LV1: 围观的猹
    2: 1.2,   // LV2: 勤劳的猹
    3: 1.5,   // LV3: 瓜地里的闰土
    4: 2.0,   // LV4: 瓜田承包户
  };

  // Daily limits for spam prevention
  static const int dailyVoteLimit = 100;
  static const int dailyPostLimit = 10;
}
