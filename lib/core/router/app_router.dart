import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../features/feed/pages/feed_page.dart';
import '../../features/story/pages/story_detail_page.dart';
import '../../features/story/pages/create_story_page.dart';
import '../../features/profile/pages/profile_page.dart';
import '../../features/auth/pages/auth_page.dart';
import '../../features/shared/widgets/animated_fab.dart';

class AppRouter {
  static final GoRouter router = GoRouter(
    initialLocation: '/feed',
    routes: [
      // Auth
      GoRoute(
        path: '/auth',
        builder: (context, state) => const AuthPage(),
      ),

      // Feed (瓜田) - with navigation wrapper
      GoRoute(
        path: '/feed',
        builder: (context, state) => const MainNavigationWrapper(child: FeedPage()),
      ),

      // Profile - with navigation wrapper
      GoRoute(
        path: '/profile',
        builder: (context, state) => const MainNavigationWrapper(child: ProfilePage()),
      ),
      
      // Story detail
      GoRoute(
        path: '/story/:id',
        builder: (context, state) {
          final storyId = state.pathParameters['id']!;
          return StoryDetailPage(storyId: storyId);
        },
      ),
      
      // Create story
      GoRoute(
        path: '/create-story',
        builder: (context, state) => const CreateStoryPage(),
      ),
    ],
  );
}

class MainNavigationWrapper extends StatelessWidget {
  final Widget child;
  
  const MainNavigationWrapper({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: child,
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        backgroundColor: Theme.of(context).colorScheme.surface,
        selectedItemColor: Theme.of(context).colorScheme.primary,
        unselectedItemColor: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
        currentIndex: _getCurrentIndex(context),
        onTap: (index) => _onTap(context, index),
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.home),
            label: '瓜田',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.person),
            label: '我的',
          ),
        ],
      ),
      floatingActionButton: AnimatedFAB(
        onPressed: () => context.push('/create-story'),
        tooltip: '发布新故事',
      ),
    );
  }
  
  int _getCurrentIndex(BuildContext context) {
    final location = GoRouterState.of(context).uri.path;
    if (location.startsWith('/feed')) return 0;
    if (location.startsWith('/profile')) return 1;
    return 0;
  }
  
  void _onTap(BuildContext context, int index) {
    switch (index) {
      case 0:
        context.go('/feed');
        break;
      case 1:
        context.go('/profile');
        break;
    }
  }
}
