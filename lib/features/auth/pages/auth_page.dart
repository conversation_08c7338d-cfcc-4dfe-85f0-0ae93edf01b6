import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../providers/auth_provider.dart';
import '../../shared/widgets/loading_animation.dart';

class AuthPage extends ConsumerStatefulWidget {
  const AuthPage({super.key});

  @override
  ConsumerState<AuthPage> createState() => _AuthPageState();
}

class _AuthPageState extends ConsumerState<AuthPage> {
  @override
  void initState() {
    super.initState();
    
    // Auto-create anonymous user
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _createAnonymousUser();
    });
  }

  Future<void> _createAnonymousUser() async {
    try {
      await ref.read(authProvider.notifier).signInAnonymously();
      if (mounted) {
        context.go('/feed');
      }
    } catch (e) {
      // Show error dialog
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('登录失败'),
            content: Text('无法创建匿名账户: $e'),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _createAnonymousUser(); // Retry
                },
                child: const Text('重试'),
              ),
            ],
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // App logo/icon
            Text(
              '🍉',
              style: TextStyle(fontSize: 80),
            ),
            const SizedBox(height: 24),
            
            Text(
              '瓜田',
              style: Theme.of(context).textTheme.headlineLarge,
            ),
            const SizedBox(height: 8),
            
            Text(
              '分享故事，站队吃瓜',
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            const SizedBox(height: 48),
            
            const LoadingAnimation(),
            const SizedBox(height: 24),
            
            Text(
              '正在为您创建账户...',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }
}
