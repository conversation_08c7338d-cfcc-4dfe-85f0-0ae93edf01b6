import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../core/theme/app_theme.dart';
import '../widgets/story_card.dart';
import '../providers/feed_provider.dart';
import '../../shared/widgets/loading_animation.dart';
import '../../shared/widgets/empty_state.dart';

class FeedPage extends ConsumerStatefulWidget {
  const FeedPage({super.key});

  @override
  ConsumerState<FeedPage> createState() => _FeedPageState();
}

class _FeedPageState extends ConsumerState<FeedPage> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    
    // Load initial stories
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(feedProvider.notifier).loadStories();
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= 
        _scrollController.position.maxScrollExtent - 200) {
      // Load more stories when near bottom
      ref.read(feedProvider.notifier).loadMoreStories();
    }
  }

  @override
  Widget build(BuildContext context) {
    final feedState = ref.watch(feedProvider);

    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            const Text('🍉'),
            const SizedBox(width: 8),
            Text(
              '瓜田',
              style: Theme.of(context).textTheme.titleLarge,
            ),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              ref.read(feedProvider.notifier).refreshStories();
            },
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          await ref.read(feedProvider.notifier).refreshStories();
        },
        child: _buildBody(feedState),
      ),
    );
  }

  Widget _buildBody(FeedState state) {
    if (state.isLoading && state.stories.isEmpty) {
      return const Center(
        child: LoadingAnimation(),
      );
    }

    if (state.stories.isEmpty && !state.isLoading) {
      return const EmptyState(
        message: '这里啥瓜都还没长出来呢',
      );
    }

    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.only(top: 8, bottom: 100),
      itemCount: state.stories.length + (state.isLoadingMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index >= state.stories.length) {
          // Loading more indicator
          return const Padding(
            padding: EdgeInsets.all(16),
            child: Center(
              child: LoadingAnimation(size: 32),
            ),
          );
        }

        final story = state.stories[index];
        return StoryCard(
          story: story,
          onTap: () {
            context.push('/story/${story.id}');
          },
        );
      },
    );
  }
}
