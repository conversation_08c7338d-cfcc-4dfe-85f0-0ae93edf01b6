import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/models/medal.dart';
import '../../../core/services/profile_service.dart';

class ProfileState {
  final List<Medal> allMedals;
  final List<UserMedal> userMedals;
  final int storiesCount;
  final int totalLikes;
  final int totalHeat;
  final int votesCount;
  final bool isLoading;
  final String? error;

  const ProfileState({
    this.allMedals = const [],
    this.userMedals = const [],
    this.storiesCount = 0,
    this.totalLikes = 0,
    this.totalHeat = 0,
    this.votesCount = 0,
    this.isLoading = false,
    this.error,
  });

  ProfileState copyWith({
    List<Medal>? allMedals,
    List<UserMedal>? userMedals,
    int? storiesCount,
    int? totalLikes,
    int? totalHeat,
    int? votesCount,
    bool? isLoading,
    String? error,
  }) {
    return ProfileState(
      allMedals: allMedals ?? this.allMedals,
      userMedals: userMedals ?? this.userMedals,
      storiesCount: storiesCount ?? this.storiesCount,
      totalLikes: totalLikes ?? this.totalLikes,
      totalHeat: totalHeat ?? this.totalHeat,
      votesCount: votesCount ?? this.votesCount,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

class ProfileNotifier extends StateNotifier<ProfileState> {
  final ProfileService _profileService;

  ProfileNotifier(this._profileService) : super(const ProfileState());

  Future<void> loadProfile() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final results = await Future.wait([
        _profileService.getAllMedals(),
        _profileService.getUserMedals(),
        _profileService.getUserStats(),
      ]);

      final allMedals = results[0] as List<Medal>;
      final userMedals = results[1] as List<UserMedal>;
      final stats = results[2] as Map<String, int>;

      state = state.copyWith(
        allMedals: allMedals,
        userMedals: userMedals,
        storiesCount: stats['stories_count'] ?? 0,
        totalLikes: stats['total_likes'] ?? 0,
        totalHeat: stats['total_heat'] ?? 0,
        votesCount: stats['votes_count'] ?? 0,
        isLoading: false,
      );
    } catch (error) {
      state = state.copyWith(
        isLoading: false,
        error: error.toString(),
      );
    }
  }

  void addMedal(UserMedal medal) {
    state = state.copyWith(
      userMedals: [...state.userMedals, medal],
    );
  }
}

final profileProvider = StateNotifierProvider<ProfileNotifier, ProfileState>((ref) {
  final profileService = ref.watch(profileServiceProvider);
  return ProfileNotifier(profileService);
});
