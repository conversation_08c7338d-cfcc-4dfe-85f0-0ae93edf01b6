import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../core/theme/app_theme.dart';
import '../widgets/medal_wall.dart';
import '../widgets/xp_progress_bar.dart';
import '../providers/profile_provider.dart';
import '../../auth/providers/auth_provider.dart';
import '../../shared/widgets/loading_animation.dart';
import '../../shared/widgets/level_badge.dart';

class ProfilePage extends ConsumerStatefulWidget {
  const ProfilePage({super.key});

  @override
  ConsumerState<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends ConsumerState<ProfilePage> {
  @override
  void initState() {
    super.initState();
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(profileProvider.notifier).loadProfile();
    });
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authProvider);
    final profileState = ref.watch(profileProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('我的'),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              // TODO: Navigate to settings
            },
          ),
        ],
      ),
      body: authState.user == null
          ? const Center(child: LoadingAnimation())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // User info card
                  _buildUserInfoCard(authState.user!),
                  const SizedBox(height: 24),
                  
                  // XP Progress
                  _buildXPSection(authState.user!),
                  const SizedBox(height: 24),
                  
                  // Medal wall
                  _buildMedalSection(profileState),
                  const SizedBox(height: 24),
                  
                  // Stats section
                  _buildStatsSection(profileState),
                ],
              ),
            ),
    );
  }

  Widget _buildUserInfoCard(user) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Row(
          children: [
            // Avatar placeholder
            Container(
              width: 64,
              height: 64,
              decoration: BoxDecoration(
                color: AppColors.guatianGreen.withOpacity(0.2),
                borderRadius: BorderRadius.circular(32),
              ),
              child: const Icon(
                Icons.person,
                size: 32,
                color: AppColors.guatianGreen,
              ),
            ),
            const SizedBox(width: 16),
            
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        '匿名用户',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(width: 8),
                      LevelBadge(level: user.level, size: 32),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'ID: ${user.id.substring(0, 8)}...',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '加入于 ${_formatDate(user.createdAt)}',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildXPSection(user) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '经验值',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 12),
        
        XPProgressBar(
          currentXP: user.xp,
          level: user.level,
        ),
        const SizedBox(height: 8),
        
        Text(
          '距离下一级还需要 ${user.xpForNextLevel - user.xp} XP',
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ],
    );
  }

  Widget _buildMedalSection(ProfileState profileState) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '勋章墙',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            if (profileState.userMedals.isNotEmpty)
              Text(
                '${profileState.userMedals.length}/${profileState.allMedals.length}',
                style: Theme.of(context).textTheme.bodySmall,
              ),
          ],
        ),
        const SizedBox(height: 12),
        
        if (profileState.isLoading)
          const Center(child: LoadingAnimation())
        else
          MedalWall(
            allMedals: profileState.allMedals,
            userMedals: profileState.userMedals,
          ),
      ],
    );
  }

  Widget _buildStatsSection(ProfileState profileState) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '我的统计',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 12),
        
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                _buildStatRow('发布故事', '${profileState.storiesCount}'),
                const Divider(),
                _buildStatRow('总获赞数', '${profileState.totalLikes}'),
                const Divider(),
                _buildStatRow('总热度', '${profileState.totalHeat}'),
                const Divider(),
                _buildStatRow('投票次数', '${profileState.votesCount}'),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodyMedium,
        ),
        Text(
          value,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            color: AppColors.guatianGreen,
          ),
        ),
      ],
    );
  }

  String _formatDate(DateTime dateTime) {
    return '${dateTime.year}年${dateTime.month}月${dateTime.day}日';
  }
}
