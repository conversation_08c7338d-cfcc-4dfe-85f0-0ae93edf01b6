import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../../core/theme/app_theme.dart';

class XPProgressBar extends StatelessWidget {
  final int currentXP;
  final int level;

  const XPProgressBar({
    super.key,
    required this.currentXP,
    required this.level,
  });

  @override
  Widget build(BuildContext context) {
    final currentLevelXP = (level - 1) * 100;
    final nextLevelXP = level * 100;
    final progressXP = currentXP - currentLevelXP;
    final totalXPNeeded = nextLevelXP - currentLevelXP;
    final progress = progressXP / totalXPNeeded;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // XP numbers
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '$progressXP XP',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppColors.guatianGreen,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              '$totalXPNeeded XP',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppColors.secondaryText,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        
        // Progress bar
        Container(
          height: 8,
          decoration: BoxDecoration(
            color: AppColors.secondaryText.withOpacity(0.2),
            borderRadius: BorderRadius.circular(4),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(4),
            child: Stack(
              children: [
                // Background
                Container(
                  width: double.infinity,
                  color: AppColors.secondaryText.withOpacity(0.2),
                ),
                
                // Progress fill
                AnimatedContainer(
                  duration: const Duration(milliseconds: 1000),
                  curve: Curves.easeInOut,
                  width: MediaQuery.of(context).size.width * progress.clamp(0.0, 1.0),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        AppColors.guatianGreen,
                        AppColors.hotMelonYellow,
                      ],
                      begin: Alignment.centerLeft,
                      end: Alignment.centerRight,
                    ),
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 4),
        
        // Level info
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              _getLevelName(level),
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppColors.guatianGreen,
              ),
            ),
            if (level < 4)
              Text(
                _getLevelName(level + 1),
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppColors.secondaryText,
                ),
              ),
          ],
        ),
      ],
    ).animate()
     .fadeIn(duration: 600.ms)
     .slideX(begin: -0.2, end: 0);
  }

  String _getLevelName(int level) {
    switch (level) {
      case 1:
        return 'LV1: 围观的猹';
      case 2:
        return 'LV2: 勤劳的猹';
      case 3:
        return 'LV3: 瓜地里的闰土';
      case 4:
        return 'LV4: 瓜田承包户';
      default:
        return 'LV$level';
    }
  }
}
