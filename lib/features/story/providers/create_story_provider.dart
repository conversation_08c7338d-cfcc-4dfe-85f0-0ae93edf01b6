import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/models/story.dart';
import '../../../core/services/story_service.dart';

class CreateStoryState {
  final bool isLoading;
  final String? error;
  final Story? createdStory;

  const CreateStoryState({
    this.isLoading = false,
    this.error,
    this.createdStory,
  });

  CreateStoryState copyWith({
    bool? isLoading,
    String? error,
    Story? createdStory,
  }) {
    return CreateStoryState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      createdStory: createdStory ?? this.createdStory,
    );
  }
}

class CreateStoryNotifier extends StateNotifier<CreateStoryState> {
  final StoryService _storyService;

  CreateStoryNotifier(this._storyService) : super(const CreateStoryState());

  Future<void> createStory({
    required String title,
    required String content,
    required String section,
    required List<String> tags,
    required String campAName,
    required String campBName,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final story = await _storyService.createStory(
        title: title,
        content: content,
        section: section,
        tags: tags,
        campAName: campAName,
        campBName: campBName,
      );

      state = state.copyWith(
        isLoading: false,
        createdStory: story,
      );
    } catch (error) {
      state = state.copyWith(
        isLoading: false,
        error: error.toString(),
      );
      rethrow;
    }
  }

  void reset() {
    state = const CreateStoryState();
  }
}

final createStoryProvider = StateNotifierProvider<CreateStoryNotifier, CreateStoryState>((ref) {
  final storyService = ref.watch(storyServiceProvider);
  return CreateStoryNotifier(storyService);
});
