import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/models/story.dart';
import '../../../core/models/vote.dart';
import '../../../core/services/story_service.dart';

final storyDetailProvider = StateNotifierProvider.family<StoryDetailNotifier, AsyncValue<Story>, String>((ref, storyId) {
  final storyService = ref.watch(storyServiceProvider);
  return StoryDetailNotifier(storyService, storyId);
});

class StoryDetailNotifier extends StateNotifier<AsyncValue<Story>> {
  final StoryService _storyService;
  final String _storyId;

  StoryDetailNotifier(this._storyService, this._storyId) : super(const AsyncValue.loading());

  Future<void> loadStory() async {
    state = const AsyncValue.loading();
    
    try {
      final story = await _storyService.getStory(_storyId);
      if (story != null) {
        state = AsyncValue.data(story);
      } else {
        state = AsyncValue.error('Story not found', StackTrace.current);
      }
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> vote({
    required VoteType voteType,
    required VoteValue voteValue,
  }) async {
    try {
      await _storyService.vote(
        storyId: _storyId,
        voteType: voteType,
        voteValue: voteValue,
      );
      
      // Reload story to get updated vote counts
      await loadStory();
    } catch (error) {
      // Handle error but don't change the state
      print('Error voting: $error');
      rethrow;
    }
  }

  Future<void> addReaction(String emoji) async {
    try {
      await _storyService.addReaction(
        storyId: _storyId,
        emoji: emoji,
      );
      
      // Reload story to get updated reaction counts
      await loadStory();
    } catch (error) {
      print('Error adding reaction: $error');
      rethrow;
    }
  }

  Future<void> removeReaction(String emoji) async {
    try {
      await _storyService.removeReaction(
        storyId: _storyId,
        emoji: emoji,
      );
      
      // Reload story to get updated reaction counts
      await loadStory();
    } catch (error) {
      print('Error removing reaction: $error');
      rethrow;
    }
  }
}
