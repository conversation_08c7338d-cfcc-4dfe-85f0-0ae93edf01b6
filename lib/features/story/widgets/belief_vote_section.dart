import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../core/models/story.dart';
import '../../../core/models/vote.dart';
import '../../../core/theme/app_theme.dart';
import '../providers/story_detail_provider.dart';

class BeliefVoteSection extends ConsumerStatefulWidget {
  final Story story;

  const BeliefVoteSection({
    super.key,
    required this.story,
  });

  @override
  ConsumerState<BeliefVoteSection> createState() => _BeliefVoteSectionState();
}

class _BeliefVoteSectionState extends ConsumerState<BeliefVoteSection> {
  bool _isVoting = false;

  // Belief vote options based on product.md
  final Map<VoteValue, String> _believeReasons = {
    VoteValue.believe: '我信',
  };

  final Map<VoteValue, String> _unbelieveReasons = {
    VoteValue.unbelieveExaggerated: '太夸张了',
    VoteValue.unbelieveFake: '像编的故事',
    VoteValue.unbelieveIncomplete: '时间线对不上',
    VoteValue.unbelieveBiased: '感觉是段子',
  };

  @override
  Widget build(BuildContext context) {
    final believeVotes = widget.story.believeVotes ?? 0;
    final unbelieveVotes = widget.story.unbelieveVotes ?? 0;
    final totalBeliefVotes = believeVotes + unbelieveVotes;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Belief statistics
        if (totalBeliefVotes > 0) ...[
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildBeliefStat('信', believeVotes, AppColors.guatianGreen),
              _buildBeliefStat('不信', unbelieveVotes, AppColors.error),
            ],
          ),
          const SizedBox(height: 16),
        ],
        
        // Vote buttons
        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _isVoting ? null : () => _showBeliefOptions(true),
                icon: const Icon(Icons.thumb_up),
                label: const Text('我信'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.guatianGreen,
                  foregroundColor: Colors.black,
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _isVoting ? null : () => _showBeliefOptions(false),
                icon: const Icon(Icons.thumb_down),
                label: const Text('我不信'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.error,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildBeliefStat(String label, int count, Color color) {
    return Column(
      children: [
        Text(
          '$count',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: color,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: color,
          ),
        ),
      ],
    );
  }

  void _showBeliefOptions(bool isBelieve) {
    showModalBottomSheet(
      context: context,
      backgroundColor: AppColors.cardBackground,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                isBelieve ? '选择相信的理由' : '选择不信的理由',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 16),
              
              if (isBelieve) ...[
                _buildReasonOption(VoteValue.believe, '我信这个故事'),
              ] else ...[
                ..._unbelieveReasons.entries.map((entry) {
                  return _buildReasonOption(entry.key, entry.value);
                }),
              ],
              
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('取消'),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildReasonOption(VoteValue voteValue, String reason) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        title: Text(reason),
        onTap: () {
          Navigator.of(context).pop();
          _vote(voteValue);
        },
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        tileColor: AppColors.primaryBackground,
      ),
    );
  }

  Future<void> _vote(VoteValue voteValue) async {
    if (_isVoting) return;

    setState(() {
      _isVoting = true;
    });

    try {
      await ref.read(storyDetailProvider(widget.story.id).notifier).vote(
        voteType: VoteType.belief,
        voteValue: voteValue,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('已投票: ${voteValue.displayName}'),
            backgroundColor: voteValue.isBelief ? AppColors.guatianGreen : AppColors.error,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('投票失败: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isVoting = false;
        });
      }
    }
  }
}
