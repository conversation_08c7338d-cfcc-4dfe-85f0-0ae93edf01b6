import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../../core/models/story.dart';
import '../../../core/models/reaction.dart';
import '../../../core/theme/app_theme.dart';
import '../providers/story_detail_provider.dart';

class EmojiReactionBar extends ConsumerStatefulWidget {
  final Story story;

  const EmojiReactionBar({
    super.key,
    required this.story,
  });

  @override
  ConsumerState<EmojiReactionBar> createState() => _EmojiReactionBarState();
}

class _EmojiReactionBarState extends ConsumerState<EmojiReactionBar> {
  String? _reactingEmoji;

  @override
  Widget build(BuildContext context) {
    final emojiReactions = widget.story.emojiReactions ?? {};
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Top reactions display
        if (emojiReactions.isNotEmpty) ...[
          _buildTopReactions(emojiReactions),
          const SizedBox(height: 16),
        ],
        
        // Emoji reaction buttons
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: CommonEmojis.reactions.map((emoji) {
              final count = emojiReactions[emoji] ?? 0;
              final isReacting = _reactingEmoji == emoji;
              
              return Container(
                margin: const EdgeInsets.only(right: 8),
                child: _buildEmojiButton(emoji, count, isReacting),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildTopReactions(Map<String, int> emojiReactions) {
    // Get top 3 reactions by count
    final sortedReactions = emojiReactions.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    final topReactions = sortedReactions.take(3).toList();

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.secondaryText.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: topReactions.map((entry) {
          return Column(
            children: [
              Text(
                entry.key,
                style: const TextStyle(fontSize: 24),
              ),
              const SizedBox(height: 4),
              Text(
                '${entry.value}',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  color: AppColors.guatianGreen,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                CommonEmojis.getEmojiName(entry.key),
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ],
          );
        }).toList(),
      ),
    );
  }

  Widget _buildEmojiButton(String emoji, int count, bool isReacting) {
    return GestureDetector(
      onTap: isReacting ? null : () => _toggleReaction(emoji),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: count > 0
              ? AppColors.guatianGreen.withValues(alpha: 0.2)
              : AppColors.secondaryText.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: count > 0
                ? AppColors.guatianGreen.withValues(alpha: 0.5)
                : AppColors.secondaryText.withValues(alpha: 0.3),
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              emoji,
              style: TextStyle(
                fontSize: isReacting ? 20 : 18,
              ),
            ),
            if (count > 0) ...[
              const SizedBox(width: 4),
              Text(
                '$count',
                style: Theme.of(context).textTheme.labelSmall?.copyWith(
                  color: count > 0 ? AppColors.guatianGreen : AppColors.secondaryText,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
            if (isReacting) ...[
              const SizedBox(width: 4),
              const SizedBox(
                width: 12,
                height: 12,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: AppColors.guatianGreen,
                ),
              ),
            ],
          ],
        ),
      ),
    ).animate(target: isReacting ? 1 : 0)
     .fadeIn(duration: 150.ms);
  }

  Future<void> _toggleReaction(String emoji) async {
    if (_reactingEmoji != null) return;

    setState(() {
      _reactingEmoji = emoji;
    });

    try {
      final currentReactions = widget.story.emojiReactions ?? {};
      final hasReacted = currentReactions.containsKey(emoji) && currentReactions[emoji]! > 0;

      if (hasReacted) {
        // Remove reaction
        await ref.read(storyDetailProvider(widget.story.id).notifier).removeReaction(emoji);
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('已取消 $emoji 反应'),
              duration: const Duration(seconds: 1),
            ),
          );
        }
      } else {
        // Add reaction with pop animation
        await ref.read(storyDetailProvider(widget.story.id).notifier).addReaction(emoji);
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('已添加 $emoji 反应'),
              duration: const Duration(seconds: 1),
              backgroundColor: AppColors.guatianGreen,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('操作失败: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _reactingEmoji = null;
        });
      }
    }
  }
}
