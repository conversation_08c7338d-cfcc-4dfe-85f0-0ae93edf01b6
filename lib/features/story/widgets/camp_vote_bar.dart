import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';


import '../../../core/models/story.dart';
import '../../../core/models/vote.dart';
import '../../../core/theme/app_theme.dart';
import '../providers/story_detail_provider.dart';

class CampVoteBar extends ConsumerStatefulWidget {
  final Story story;

  const CampVoteBar({
    super.key,
    required this.story,
  });

  @override
  ConsumerState<CampVoteBar> createState() => _CampVoteBarState();
}

class _CampVoteBarState extends ConsumerState<CampVoteBar> {
  bool _isVoting = false;

  @override
  Widget build(BuildContext context) {
    final campAVotes = widget.story.campAVotes ?? 0;
    final campBVotes = widget.story.campBVotes ?? 0;
    final totalVotes = campAVotes + campBVotes;
    
    final campAPercentage = totalVotes > 0 ? campAVotes / totalVotes : 0.5;
    final campBPercentage = 1.0 - campAPercentage;

    return Column(
      children: [
        // Camp names and percentages
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Text(
                widget.story.campAName,
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  color: AppColors.guatianGreen,
                ),
                textAlign: TextAlign.left,
              ),
            ),
            Text(
              'VS',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                color: AppColors.secondaryText,
              ),
            ),
            Expanded(
              child: Text(
                widget.story.campBName,
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  color: AppColors.hotMelonYellow,
                ),
                textAlign: TextAlign.right,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        
        // Vote percentages
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '${(campAPercentage * 100).toInt()}%',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: AppColors.guatianGreen,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              '${(campBPercentage * 100).toInt()}%',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: AppColors.hotMelonYellow,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        
        // Animated vote bar
        Container(
          height: 12,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(6),
            color: AppColors.secondaryText.withOpacity(0.2),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(6),
            child: Row(
              children: [
                AnimatedContainer(
                  duration: const Duration(milliseconds: 800),
                  curve: Curves.easeInOut,
                  width: MediaQuery.of(context).size.width * 0.8 * campAPercentage,
                  decoration: const BoxDecoration(
                    color: AppColors.guatianGreen,
                  ),
                ),
                AnimatedContainer(
                  duration: const Duration(milliseconds: 800),
                  curve: Curves.easeInOut,
                  width: MediaQuery.of(context).size.width * 0.8 * campBPercentage,
                  decoration: const BoxDecoration(
                    color: AppColors.hotMelonYellow,
                  ),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 16),
        
        // Vote buttons
        Row(
          children: [
            Expanded(
              child: ElevatedButton(
                onPressed: _isVoting ? null : () => _vote(VoteValue.campA),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.guatianGreen,
                  foregroundColor: Colors.black,
                ),
                child: _isVoting
                    ? const SizedBox(
                        height: 16,
                        width: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: Colors.black,
                        ),
                      )
                    : Text('支持 ${widget.story.campAName}'),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: ElevatedButton(
                onPressed: _isVoting ? null : () => _vote(VoteValue.campB),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.hotMelonYellow,
                  foregroundColor: Colors.black,
                ),
                child: _isVoting
                    ? const SizedBox(
                        height: 16,
                        width: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: Colors.black,
                        ),
                      )
                    : Text('支持 ${widget.story.campBName}'),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        
        // Vote count
        Text(
          '总投票数: $totalVotes',
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ],
    );
  }

  Future<void> _vote(VoteValue voteValue) async {
    if (_isVoting) return;

    setState(() {
      _isVoting = true;
    });

    try {
      await ref.read(storyDetailProvider(widget.story.id).notifier).vote(
        voteType: VoteType.camp,
        voteValue: voteValue,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('已投票支持 ${voteValue == VoteValue.campA ? widget.story.campAName : widget.story.campBName}'),
            backgroundColor: voteValue == VoteValue.campA ? AppColors.guatianGreen : AppColors.hotMelonYellow,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('投票失败: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isVoting = false;
        });
      }
    }
  }
}
