import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../core/models/story.dart';
import '../widgets/camp_vote_bar.dart';
import '../widgets/belief_vote_section.dart';
import '../widgets/emoji_reaction_bar.dart';
import '../providers/story_detail_provider.dart';
import '../../shared/widgets/loading_animation.dart';
import '../../shared/widgets/level_badge.dart';

class StoryDetailPage extends ConsumerStatefulWidget {
  final String storyId;

  const StoryDetailPage({
    super.key,
    required this.storyId,
  });

  @override
  ConsumerState<StoryDetailPage> createState() => _StoryDetailPageState();
}

class _StoryDetailPageState extends ConsumerState<StoryDetailPage> {
  @override
  void initState() {
    super.initState();
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(storyDetailProvider(widget.storyId).notifier).loadStory();
    });
  }

  @override
  Widget build(BuildContext context) {
    final storyState = ref.watch(storyDetailProvider(widget.storyId));

    return Scaffold(
      appBar: AppBar(
        title: const Text('故事详情'),
        actions: [
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () {
              // TODO: Implement sharing
            },
          ),
        ],
      ),
      body: storyState.when(
        loading: () => const Center(child: LoadingAnimation()),
        error: (error, stack) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, size: 64),
              const SizedBox(height: 16),
              Text('加载失败: $error'),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  ref.read(storyDetailProvider(widget.storyId).notifier).loadStory();
                },
                child: const Text('重试'),
              ),
            ],
          ),
        ),
        data: (story) => _buildStoryContent(story),
      ),
    );
  }

  Widget _buildStoryContent(Story story) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Author info
          _buildAuthorInfo(story),
          const SizedBox(height: 16),
          
          // Title
          Text(
            story.title,
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 16),
          
          // Content
          Text(
            story.content,
            style: Theme.of(context).textTheme.bodyLarge,
          ),
          const SizedBox(height: 16),
          
          // Tags
          if (story.tags.isNotEmpty) ...[
            _buildTags(story.tags),
            const SizedBox(height: 24),
          ],
          
          // Voting section
          _buildVotingSection(story),
          
          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _buildAuthorInfo(Story story) {
    return Row(
      children: [
        LevelBadge(level: story.authorLevel ?? 1),
        const SizedBox(width: 12),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '匿名用户',
              style: Theme.of(context).textTheme.titleSmall,
            ),
            Text(
              story.section,
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
        const Spacer(),
        Text(
          _formatTime(story.createdAt),
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ],
    );
  }

  Widget _buildTags(List<String> tags) {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: tags.map((tag) {
        return Chip(
          label: Text(tag),
          backgroundColor: Theme.of(context).colorScheme.surface,
        );
      }).toList(),
    );
  }

  Widget _buildVotingSection(Story story) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '站队支持',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 12),
        
        // Camp vote bar
        CampVoteBar(story: story),
        const SizedBox(height: 24),
        
        Text(
          '信/不信',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 12),
        
        // Belief vote section
        BeliefVoteSection(story: story),
        const SizedBox(height: 24),
        
        Text(
          'Emoji反应',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 12),
        
        // Emoji reaction bar
        EmojiReactionBar(story: story),
      ],
    );
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return '刚刚';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}分钟前';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}小时前';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}天前';
    } else {
      return '${dateTime.month}月${dateTime.day}日';
    }
  }
}
