import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../core/theme/app_theme.dart';

class SplashAnimation extends StatelessWidget {
  final VoidCallback? onComplete;

  const SplashAnimation({
    super.key,
    this.onComplete,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.primaryBackground,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Main logo animation
            _buildLogoAnimation(),
            const SizedBox(height: 32),
            
            // App name
            Text(
              '瓜田',
              style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                color: AppColors.guatianGreen,
              ),
            )
                .animate()
                .fadeIn(delay: 1000.ms, duration: 800.ms)
                .scale(begin: const Offset(0.8, 0.8), end: const Offset(1.0, 1.0))
                .shimmer(
                  delay: 1500.ms,
                  duration: 1000.ms,
                  color: AppColors.hotMelonYellow,
                ),
            
            const SizedBox(height: 16),
            
            // Tagline
            Text(
              '分享故事，站队吃瓜',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: AppColors.bodyText,
              ),
            )
                .animate()
                .fadeIn(delay: 1500.ms, duration: 600.ms)
                .slideY(begin: 0.3, end: 0),
            
            const SizedBox(height: 48),
            
            // Loading indicator
            const LoadingAnimation()
                .animate()
                .fadeIn(delay: 2000.ms, duration: 400.ms),
          ],
        ),
      ),
    );
  }

  Widget _buildLogoAnimation() {
    return Stack(
      alignment: Alignment.center,
      children: [
        // Background glow
        Container(
          width: 120,
          height: 120,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: RadialGradient(
              colors: [
                AppColors.guatianGreen.withOpacity(0.3),
                AppColors.guatianGreen.withOpacity(0.1),
                Colors.transparent,
              ],
            ),
          ),
        )
            .animate(onPlay: (controller) => controller.repeat(reverse: true))
            .scale(
              begin: const Offset(0.8, 0.8),
              end: const Offset(1.2, 1.2),
              duration: 2000.ms,
              curve: Curves.easeInOut,
            ),
        
        // Main watermelon
        Text(
          '🍉',
          style: const TextStyle(fontSize: 80),
        )
            .animate()
            .scale(
              begin: const Offset(0.0, 0.0),
              end: const Offset(1.0, 1.0),
              duration: 800.ms,
              curve: Curves.elasticOut,
            )
            .then()
            .rotate(
              begin: 0,
              end: 0.1,
              duration: 1000.ms,
              curve: Curves.easeInOut,
            )
            .then()
            .rotate(
              begin: 0.1,
              end: -0.1,
              duration: 1000.ms,
              curve: Curves.easeInOut,
            )
            .then()
            .rotate(
              begin: -0.1,
              end: 0,
              duration: 1000.ms,
              curve: Curves.easeInOut,
            ),
        
        // Sparkle effects
        ...List.generate(6, (index) {
          const radius = 60.0;
          
          return Positioned(
            left: radius * 1.5 + radius * 0.8 * (index % 2 == 0 ? 1 : -1),
            top: radius * 1.5 + radius * 0.8 * (index < 3 ? -1 : 1),
            child: Text(
              '✨',
              style: const TextStyle(fontSize: 16),
            )
                .animate(
                  onPlay: (controller) => controller.repeat(),
                )
                .fadeIn(
                  delay: (500 + index * 200).ms,
                  duration: 400.ms,
                )
                .then()
                .fadeOut(duration: 400.ms)
                .scale(
                  begin: const Offset(0.5, 0.5),
                  end: const Offset(1.0, 1.0),
                  duration: 800.ms,
                ),
          );
        }),
      ],
    );
  }
}

// Import the LoadingAnimation widget
class LoadingAnimation extends StatelessWidget {
  final double size;

  const LoadingAnimation({
    super.key,
    this.size = 32,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: size * 2,
      height: size,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Running track
          Container(
            width: size * 2,
            height: 3,
            decoration: BoxDecoration(
              color: AppColors.secondaryText.withOpacity(0.2),
              borderRadius: BorderRadius.circular(1.5),
            ),
          ),
          
          // Running character
          Text(
            '🐹',
            style: TextStyle(fontSize: size * 0.6),
          )
              .animate(onPlay: (controller) => controller.repeat())
              .slideX(
                begin: -1.0,
                end: 1.0,
                duration: 1500.ms,
                curve: Curves.easeInOut,
              ),
        ],
      ),
    );
  }
}
