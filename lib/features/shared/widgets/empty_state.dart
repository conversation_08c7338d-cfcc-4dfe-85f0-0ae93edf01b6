import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../core/theme/app_theme.dart';

class EmptyState extends StatelessWidget {
  final String message;
  final String? actionText;
  final VoidCallback? onAction;

  const EmptyState({
    super.key,
    required this.message,
    this.actionText,
    this.onAction,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Animated shrugging 猹 illustration
            _buildShrugAnimation(),
            const SizedBox(height: 24),

            Text(
              message,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: AppColors.secondaryText,
              ),
              textAlign: TextAlign.center,
            )
                .animate()
                .fadeIn(delay: 500.ms, duration: 600.ms)
                .slideY(begin: 0.2, end: 0),

            if (actionText != null && onAction != null) ...[
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: onAction,
                child: Text(actionText!),
              )
                  .animate()
                  .fadeIn(delay: 800.ms, duration: 600.ms),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildShrugAnimation() {
    return Stack(
      alignment: Alignment.center,
      children: [
        // Main character (using hamster as placeholder for 猹)
        Text(
          '🐹',
          style: const TextStyle(fontSize: 64),
        )
            .animate(onPlay: (controller) => controller.repeat(reverse: true))
            .rotate(
              begin: -0.05,
              end: 0.05,
              duration: 2000.ms,
              curve: Curves.easeInOut,
            )
,

        // Floating question marks
        ...List.generate(3, (index) {
          return Positioned(
            left: 20 + (index * 15.0),
            top: -10 - (index * 8.0),
            child: Text(
              '?',
              style: TextStyle(
                fontSize: 16 - (index * 2.0),
                color: AppColors.secondaryText.withOpacity(0.7 - (index * 0.2)),
              ),
            )
                .animate(onPlay: (controller) => controller.repeat())
                .fadeIn(
                  delay: (index * 500).ms,
                  duration: 1000.ms,
                )
                .then()
                .fadeOut(duration: 1000.ms)
                .slideY(
                  begin: 0,
                  end: -0.5,
                  duration: 2000.ms,
                ),
          );
        }),

        // Tumbleweeds effect
        Positioned(
          bottom: -20,
          right: -30,
          child: Text(
            '🌾',
            style: const TextStyle(fontSize: 20),
          )
              .animate(onPlay: (controller) => controller.repeat())
              .slideX(
                begin: 1.5,
                end: -1.5,
                duration: 8000.ms,
                curve: Curves.linear,
              )
              .rotate(
                begin: 0,
                end: 4,
                duration: 8000.ms,
              ),
        ),
      ],
    );
  }
}
