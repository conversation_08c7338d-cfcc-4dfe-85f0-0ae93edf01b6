import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../core/theme/app_theme.dart';

class AnimatedFAB extends StatefulWidget {
  final VoidCallback onPressed;
  final String tooltip;
  final IconData icon;

  const AnimatedFAB({
    super.key,
    required this.onPressed,
    this.tooltip = '发布故事',
    this.icon = Icons.add,
  });

  @override
  State<AnimatedFAB> createState() => _AnimatedFABState();
}

class _AnimatedFABState extends State<AnimatedFAB>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _rotateController;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    )..repeat();
    
    _rotateController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _rotateController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.center,
      children: [
        // Pulse ring animation
        AnimatedBuilder(
          animation: _pulseController,
          builder: (context, child) {
            return Container(
              width: 80 + (20 * _pulseController.value),
              height: 80 + (20 * _pulseController.value),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: AppColors.guatianGreen.withOpacity(
                    0.5 * (1 - _pulseController.value),
                  ),
                  width: 2,
                ),
              ),
            );
          },
        ),
        
        // Main FAB
        GestureDetector(
          onTapDown: (_) {
            setState(() => _isPressed = true);
            _rotateController.forward();
          },
          onTapUp: (_) {
            setState(() => _isPressed = false);
            _rotateController.reverse();
            widget.onPressed();
          },
          onTapCancel: () {
            setState(() => _isPressed = false);
            _rotateController.reverse();
          },
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 150),
            width: _isPressed ? 52 : 56,
            height: _isPressed ? 52 : 56,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  AppColors.guatianGreen,
                  AppColors.hotMelonYellow,
                ],
              ),
              boxShadow: [
                BoxShadow(
                  color: AppColors.guatianGreen.withOpacity(0.4),
                  blurRadius: _isPressed ? 8 : 12,
                  offset: Offset(0, _isPressed ? 2 : 4),
                ),
              ],
            ),
            child: AnimatedBuilder(
              animation: _rotateController,
              builder: (context, child) {
                return Transform.rotate(
                  angle: _rotateController.value * 0.5,
                  child: Icon(
                    widget.icon,
                    color: Colors.black,
                    size: 28,
                  ),
                );
              },
            ),
          ),
        ),
        
        // Sparkle effects on tap
        if (_isPressed) ...[
          ...List.generate(6, (index) {
            final angle = (index * 60.0) * (3.14159 / 180);
            final radius = 40.0;
            
            return Positioned(
              left: radius + radius * 0.8 * (index % 2 == 0 ? 1 : -1),
              top: radius + radius * 0.8 * (index < 3 ? -1 : 1),
              child: Text(
                '✨',
                style: const TextStyle(fontSize: 12),
              )
                  .animate()
                  .fadeIn(duration: 200.ms)
                  .then()
                  .fadeOut(duration: 300.ms),
            );
          }),
        ],
      ],
    );
  }
}

class MelonFAB extends StatelessWidget {
  final VoidCallback onPressed;
  final String tooltip;

  const MelonFAB({
    super.key,
    required this.onPressed,
    this.tooltip = '发布故事',
  });

  @override
  Widget build(BuildContext context) {
    return Tooltip(
      message: tooltip,
      child: GestureDetector(
        onTap: onPressed,
        child: Container(
          width: 56,
          height: 56,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: RadialGradient(
              colors: [
                AppColors.guatianGreen,
                AppColors.guatianGreen.withOpacity(0.8),
              ],
            ),
            boxShadow: [
              BoxShadow(
                color: AppColors.guatianGreen.withOpacity(0.4),
                blurRadius: 12,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: const Center(
            child: Text(
              '🍉',
              style: TextStyle(fontSize: 28),
            ),
          ),
        )
            .animate(onPlay: (controller) => controller.repeat(reverse: true))
            .rotate(
              begin: -0.05,
              end: 0.05,
              duration: 2000.ms,
              curve: Curves.easeInOut,
            ),
      ),
    );
  }
}
