import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:guatian/main.dart' as app;

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Guatian App Integration Tests', () {
    testWidgets('should complete basic user flow', (WidgetTester tester) async {
      // Start the app
      app.main();
      await tester.pumpAndSettle();

      // Should start with auth page (anonymous login)
      expect(find.text('瓜田'), findsOneWidget);
      expect(find.text('正在为您创建账户...'), findsOneWidget);

      // Wait for auth to complete and navigate to feed
      await tester.pumpAndSettle(const Duration(seconds: 5));

      // Should be on feed page
      expect(find.text('瓜田'), findsOneWidget);
      
      // Should show empty state if no stories
      expect(find.text('这里啥瓜都还没长出来呢'), findsAny);

      // Test navigation to create story
      final fab = find.byType(FloatingActionButton);
      if (fab.evaluate().isNotEmpty) {
        await tester.tap(fab);
        await tester.pumpAndSettle();

        // Should be on create story page
        expect(find.text('发布故事'), findsOneWidget);
        expect(find.text('选择板块'), findsOneWidget);

        // Go back to feed
        await tester.pageBack();
        await tester.pumpAndSettle();
      }

      // Test navigation to profile
      final profileTab = find.text('我的');
      if (profileTab.evaluate().isNotEmpty) {
        await tester.tap(profileTab);
        await tester.pumpAndSettle();

        // Should be on profile page
        expect(find.text('我的'), findsOneWidget);
        expect(find.text('匿名用户'), findsOneWidget);
        expect(find.text('经验值'), findsOneWidget);
        expect(find.text('勋章墙'), findsOneWidget);

        // Go back to feed
        final feedTab = find.text('瓜田');
        await tester.tap(feedTab);
        await tester.pumpAndSettle();
      }
    });

    testWidgets('should handle story creation flow', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Wait for auth
      await tester.pumpAndSettle(const Duration(seconds: 5));

      // Navigate to create story
      final fab = find.byType(FloatingActionButton);
      if (fab.evaluate().isNotEmpty) {
        await tester.tap(fab);
        await tester.pumpAndSettle();

        // Fill out the form
        await tester.enterText(
          find.widgetWithText(TextFormField, '给你的故事起个吸引人的标题'),
          '测试故事标题',
        );

        await tester.enterText(
          find.widgetWithText(TextFormField, '详细描述你的故事...'),
          '这是一个测试故事的内容，用于验证应用的基本功能是否正常工作。',
        );

        await tester.enterText(
          find.widgetWithText(TextFormField, '阵营A名称'),
          '支持方',
        );

        await tester.enterText(
          find.widgetWithText(TextFormField, '阵营B名称'),
          '反对方',
        );

        // Try to submit (will fail without proper backend setup)
        final submitButton = find.text('发布故事');
        if (submitButton.evaluate().isNotEmpty) {
          await tester.tap(submitButton);
          await tester.pumpAndSettle();

          // Should show error or success message
          expect(find.byType(SnackBar), findsAny);
        }
      }
    });

    testWidgets('should handle theme and animations', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Check that dark theme is applied
      final materialApp = tester.widget<MaterialApp>(find.byType(MaterialApp));
      expect(materialApp.theme?.brightness, Brightness.dark);

      // Check for loading animation
      expect(find.byType(CircularProgressIndicator), findsAny);

      // Wait for animations to complete
      await tester.pumpAndSettle(const Duration(seconds: 3));
    });

    testWidgets('should handle navigation correctly', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Wait for auth
      await tester.pumpAndSettle(const Duration(seconds: 5));

      // Test bottom navigation
      final bottomNavBar = find.byType(BottomNavigationBar);
      if (bottomNavBar.evaluate().isNotEmpty) {
        // Should have 2 tabs
        final navItems = find.byType(BottomNavigationBarItem);
        expect(navItems.evaluate().length, 2);

        // Test switching between tabs
        final profileTab = find.text('我的');
        if (profileTab.evaluate().isNotEmpty) {
          await tester.tap(profileTab);
          await tester.pumpAndSettle();

          final feedTab = find.text('瓜田');
          await tester.tap(feedTab);
          await tester.pumpAndSettle();
        }
      }
    });

    testWidgets('should display correct UI elements', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Wait for auth
      await tester.pumpAndSettle(const Duration(seconds: 5));

      // Check for key UI elements
      expect(find.text('瓜田'), findsOneWidget);
      expect(find.byType(AppBar), findsOneWidget);
      expect(find.byType(FloatingActionButton), findsOneWidget);
      expect(find.byType(BottomNavigationBar), findsOneWidget);

      // Check for refresh functionality
      final refreshIndicator = find.byType(RefreshIndicator);
      if (refreshIndicator.evaluate().isNotEmpty) {
        await tester.drag(refreshIndicator, const Offset(0, 300));
        await tester.pumpAndSettle();
      }
    });
  });
}
