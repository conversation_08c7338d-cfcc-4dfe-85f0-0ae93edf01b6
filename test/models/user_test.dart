import 'package:flutter_test/flutter_test.dart';
import 'package:guatian/core/models/user.dart';

void main() {
  group('User Model Tests', () {
    test('should create user from JSON correctly', () {
      final json = {
        'id': 'test-user-id',
        'level': 2,
        'xp': 150,
        'created_at': '2024-01-01T00:00:00Z',
      };

      final user = User.fromJson(json);

      expect(user.id, 'test-user-id');
      expect(user.level, 2);
      expect(user.xp, 150);
      expect(user.createdAt, DateTime.parse('2024-01-01T00:00:00Z'));
    });

    test('should convert user to JSON correctly', () {
      final user = User(
        id: 'test-user-id',
        level: 3,
        xp: 250,
        createdAt: DateTime.parse('2024-01-01T00:00:00Z'),
      );

      final json = user.toJson();

      expect(json['id'], 'test-user-id');
      expect(json['level'], 3);
      expect(json['xp'], 250);
      expect(json['created_at'], '2024-01-01T00:00:00.000Z');
    });

    test('should calculate XP for next level correctly', () {
      final user = User(
        id: 'test-user-id',
        level: 2,
        xp: 150,
        createdAt: DateTime.now(),
      );

      expect(user.xpForNextLevel, 200); // level 2 * 100
    });

    test('should calculate level progress correctly', () {
      final user = User(
        id: 'test-user-id',
        level: 2,
        xp: 150, // 50 XP into level 2 (100-200 range)
        createdAt: DateTime.now(),
      );

      expect(user.levelProgress, 0.5); // 50/100 = 0.5
    });

    test('should handle level 1 progress correctly', () {
      final user = User(
        id: 'test-user-id',
        level: 1,
        xp: 50,
        createdAt: DateTime.now(),
      );

      expect(user.levelProgress, 0.5); // 50/100 = 0.5
    });

    test('should create copy with updated values', () {
      final originalUser = User(
        id: 'test-user-id',
        level: 1,
        xp: 50,
        createdAt: DateTime.now(),
      );

      final updatedUser = originalUser.copyWith(
        level: 2,
        xp: 150,
      );

      expect(updatedUser.id, originalUser.id);
      expect(updatedUser.level, 2);
      expect(updatedUser.xp, 150);
      expect(updatedUser.createdAt, originalUser.createdAt);
    });

    test('should handle equality correctly', () {
      final date = DateTime.now();
      final user1 = User(
        id: 'test-user-id',
        level: 1,
        xp: 50,
        createdAt: date,
      );

      final user2 = User(
        id: 'test-user-id',
        level: 1,
        xp: 50,
        createdAt: date,
      );

      final user3 = User(
        id: 'different-id',
        level: 1,
        xp: 50,
        createdAt: date,
      );

      expect(user1, equals(user2));
      expect(user1, isNot(equals(user3)));
    });
  });
}
