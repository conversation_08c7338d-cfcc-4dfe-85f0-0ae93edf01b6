import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:guatian/features/feed/widgets/story_card.dart';
import 'package:guatian/core/models/story.dart';
import 'package:guatian/core/theme/app_theme.dart';

void main() {
  group('StoryCard Widget Tests', () {
    late Story testStory;

    setUp(() {
      testStory = Story(
        id: 'test-story-id',
        authorId: 'test-author-id',
        title: 'Test Story Title',
        content: 'This is a test story content that should be displayed in the card.',
        section: '职场那些事儿',
        tags: ['#测试', '#故事'],
        campAName: '支持方',
        campBName: '反对方',
        heat: 100,
        createdAt: DateTime(2024, 1, 1),
        campAVotes: 30,
        campBVotes: 70,
        authorLevel: 2,
      );
    });

    Widget createTestWidget(Story story, {VoidCallback? onTap}) {
      return MaterialApp(
        theme: AppTheme.darkTheme,
        home: Scaffold(
          body: StoryCard(
            story: story,
            onTap: onTap ?? () {},
          ),
        ),
      );
    }

    testWidgets('should display story title', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(testStory));

      expect(find.text('Test Story Title'), findsOneWidget);
    });

    testWidgets('should display story content preview', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(testStory));

      expect(find.textContaining('This is a test story content'), findsOneWidget);
    });

    testWidgets('should display section', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(testStory));

      expect(find.text('职场那些事儿'), findsOneWidget);
    });

    testWidgets('should display tags', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(testStory));

      expect(find.text('#测试'), findsOneWidget);
      expect(find.text('#故事'), findsOneWidget);
    });

    testWidgets('should display heat value', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(testStory));

      expect(find.text('100'), findsOneWidget);
    });

    testWidgets('should display level badge', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(testStory));

      expect(find.text('L2'), findsOneWidget);
    });

    testWidgets('should display camp vote preview', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(testStory));

      // Should show the camp vote preview bar
      expect(find.byType(Container), findsWidgets);
    });

    testWidgets('should call onTap when tapped', (WidgetTester tester) async {
      bool wasTapped = false;
      
      await tester.pumpWidget(createTestWidget(
        testStory,
        onTap: () => wasTapped = true,
      ));

      await tester.tap(find.byType(StoryCard));
      await tester.pump();

      expect(wasTapped, true);
    });

    testWidgets('should show animation on tap', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(testStory));

      // Find the GestureDetector
      final gestureDetector = find.byType(GestureDetector);
      expect(gestureDetector, findsOneWidget);

      // Simulate tap down
      await tester.startGesture(tester.getCenter(gestureDetector));
      await tester.pump();

      // Should trigger animation (scale down)
      // Note: Testing animations requires more complex setup with AnimationController
    });

    testWidgets('should handle story without tags', (WidgetTester tester) async {
      final storyWithoutTags = testStory.copyWith(tags: []);
      
      await tester.pumpWidget(createTestWidget(storyWithoutTags));

      expect(find.text('#测试'), findsNothing);
      expect(find.text('#故事'), findsNothing);
    });

    testWidgets('should handle story without votes', (WidgetTester tester) async {
      final storyWithoutVotes = testStory.copyWith(
        campAVotes: null,
        campBVotes: null,
      );
      
      await tester.pumpWidget(createTestWidget(storyWithoutVotes));

      // Should still render without errors
      expect(find.text('Test Story Title'), findsOneWidget);
    });

    testWidgets('should limit tags display to 3', (WidgetTester tester) async {
      final storyWithManyTags = testStory.copyWith(
        tags: ['#tag1', '#tag2', '#tag3', '#tag4', '#tag5'],
      );
      
      await tester.pumpWidget(createTestWidget(storyWithManyTags));

      expect(find.text('#tag1'), findsOneWidget);
      expect(find.text('#tag2'), findsOneWidget);
      expect(find.text('#tag3'), findsOneWidget);
      expect(find.text('#tag4'), findsNothing);
      expect(find.text('#tag5'), findsNothing);
    });
  });
}
