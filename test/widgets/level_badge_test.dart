import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:guatian/features/shared/widgets/level_badge.dart';
import 'package:guatian/core/theme/app_theme.dart';

void main() {
  group('LevelBadge Widget Tests', () {
    Widget createTestWidget(int level, {double? size}) {
      return MaterialApp(
        theme: AppTheme.darkTheme,
        home: Scaffold(
          body: LevelBadge(
            level: level,
            size: size,
          ),
        ),
      );
    }

    testWidgets('should display correct level text', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(1));
      expect(find.text('L1'), findsOneWidget);

      await tester.pumpWidget(createTestWidget(5));
      expect(find.text('L5'), findsOneWidget);
    });

    testWidgets('should have correct default size', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(1));

      final container = tester.widget<Container>(find.byType(Container));
      expect(container.constraints?.maxWidth, 24);
      expect(container.constraints?.maxHeight, 24);
    });

    testWidgets('should respect custom size', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(1, size: 32));

      final container = tester.widget<Container>(find.byType(Container));
      expect(container.constraints?.maxWidth, 32);
      expect(container.constraints?.maxHeight, 32);
    });

    testWidgets('should have gradient decoration', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(1));

      final container = tester.widget<Container>(find.byType(Container));
      final decoration = container.decoration as BoxDecoration;
      
      expect(decoration.gradient, isA<LinearGradient>());
      expect(decoration.shape, BoxShape.circle);
    });

    testWidgets('should have correct text color', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(1));

      final text = tester.widget<Text>(find.text('L1'));
      expect(text.style?.color, Colors.black);
    });

    testWidgets('should scale text size with badge size', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(1, size: 40));

      final text = tester.widget<Text>(find.text('L1'));
      expect(text.style?.fontSize, 40 * 0.4); // size * 0.4
    });

    testWidgets('should handle different level numbers', (WidgetTester tester) async {
      // Test single digit
      await tester.pumpWidget(createTestWidget(1));
      expect(find.text('L1'), findsOneWidget);

      // Test double digit
      await tester.pumpWidget(createTestWidget(10));
      expect(find.text('L10'), findsOneWidget);

      // Test triple digit
      await tester.pumpWidget(createTestWidget(100));
      expect(find.text('L100'), findsOneWidget);
    });

    testWidgets('should have border', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(1));

      final container = tester.widget<Container>(find.byType(Container));
      final decoration = container.decoration as BoxDecoration;
      
      expect(decoration.border, isA<Border>());
    });
  });
}
