import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface VoteData {
  vote_type: string;
  vote_value: string;
  user_level: number;
}

interface ReactionData {
  emoji_text: string;
  user_level: number;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { story_id } = await req.json()

    if (!story_id) {
      return new Response(
        JSON.stringify({ error: 'story_id is required' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Create Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Get all votes for the story with user levels
    const { data: votes, error: votesError } = await supabaseClient
      .from('votes')
      .select(`
        vote_type,
        vote_value,
        users!votes_user_id_fkey(level)
      `)
      .eq('story_id', story_id)

    if (votesError) {
      throw votesError
    }

    // Get all reactions for the story with user levels
    const { data: reactions, error: reactionsError } = await supabaseClient
      .from('reactions')
      .select(`
        emoji_text,
        users!reactions_user_id_fkey(level)
      `)
      .eq('story_id', story_id)

    if (reactionsError) {
      throw reactionsError
    }

    // Calculate heat based on product.md algorithm
    // Heat = (Emoji total) + (Camp votes weighted) + (Belief votes weighted)
    
    // Level weights from product.md
    const levelWeights: { [key: number]: number } = {
      1: 1.0,   // LV1: 围观的猹
      2: 1.2,   // LV2: 勤劳的猹
      3: 1.5,   // LV3: 瓜地里的闰土
      4: 2.0,   // LV4: 瓜田承包户
    }

    let totalHeat = 0

    // Calculate emoji heat (each emoji = 1 point, no level weighting for simplicity)
    const emojiHeat = reactions?.length || 0
    totalHeat += emojiHeat

    // Calculate vote heat with level weighting
    let campVoteHeat = 0
    let beliefVoteHeat = 0

    votes?.forEach((vote: any) => {
      const userLevel = vote.users?.level || 1
      const weight = levelWeights[userLevel] || 1.0

      if (vote.vote_type === 'camp') {
        campVoteHeat += weight
      } else if (vote.vote_type === 'belief') {
        beliefVoteHeat += weight
      }
    })

    totalHeat += campVoteHeat + beliefVoteHeat

    // Round to nearest integer
    const finalHeat = Math.round(totalHeat)

    // Update story heat
    const { error: updateError } = await supabaseClient
      .from('stories')
      .update({ heat: finalHeat })
      .eq('id', story_id)

    if (updateError) {
      throw updateError
    }

    // Log calculation details for debugging
    console.log(`Heat calculation for story ${story_id}:`, {
      emojiHeat,
      campVoteHeat,
      beliefVoteHeat,
      finalHeat,
      votesCount: votes?.length || 0,
      reactionsCount: reactions?.length || 0
    })

    return new Response(
      JSON.stringify({ 
        success: true, 
        story_id,
        heat: finalHeat,
        breakdown: {
          emoji_heat: emojiHeat,
          camp_vote_heat: campVoteHeat,
          belief_vote_heat: beliefVoteHeat
        }
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('Error calculating heat:', error)
    
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        details: error.message 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
