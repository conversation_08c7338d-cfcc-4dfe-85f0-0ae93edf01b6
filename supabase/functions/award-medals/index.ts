import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { user_id, event_type } = await req.json()

    if (!user_id) {
      return new Response(
        JSON.stringify({ error: 'user_id is required' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Create Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Get user's current medals
    const { data: currentMedals, error: currentMedalsError } = await supabaseClient
      .from('user_medals')
      .select('medal_id')
      .eq('user_id', user_id)

    if (currentMedalsError) {
      throw currentMedalsError
    }

    const currentMedalIds = new Set(currentMedals?.map(m => m.medal_id) || [])

    // Check which medals the user qualifies for
    const { data: qualifyingMedals, error: qualifyingError } = await supabaseClient
      .rpc('check_user_medals', { user_id_param: user_id })

    if (qualifyingError) {
      throw qualifyingError
    }

    // Find new medals to award
    const newMedals = qualifyingMedals?.filter(medal => 
      !currentMedalIds.has(medal.medal_id)
    ) || []

    let awardedMedals = []

    // Award new medals
    for (const medal of newMedals) {
      const { error: awardError } = await supabaseClient
        .from('user_medals')
        .insert({
          user_id,
          medal_id: medal.medal_id
        })

      if (awardError) {
        console.error(`Error awarding medal ${medal.medal_name}:`, awardError)
      } else {
        awardedMedals.push(medal)
        console.log(`Awarded medal "${medal.medal_name}" to user ${user_id}`)
      }
    }

    // Special handling for event-specific medals
    if (event_type === 'registration') {
      // Award newcomer medal immediately
      const { data: newcomerMedal } = await supabaseClient
        .from('medals')
        .select('id, name')
        .eq('name', '瓜田新人')
        .single()

      if (newcomerMedal && !currentMedalIds.has(newcomerMedal.id)) {
        const { error: awardError } = await supabaseClient
          .from('user_medals')
          .insert({
            user_id,
            medal_id: newcomerMedal.id
          })

        if (!awardError) {
          awardedMedals.push({
            medal_id: newcomerMedal.id,
            medal_name: newcomerMedal.name
          })
        }
      }
    }

    // Check for level progression and award XP if needed
    if (event_type === 'story_created') {
      await awardXP(supabaseClient, user_id, 10) // 10 XP for creating a story
    } else if (event_type === 'vote_received') {
      await awardXP(supabaseClient, user_id, 2) // 2 XP for receiving a vote
    } else if (event_type === 'reaction_received') {
      await awardXP(supabaseClient, user_id, 1) // 1 XP for receiving a reaction
    }

    return new Response(
      JSON.stringify({ 
        success: true,
        user_id,
        event_type,
        awarded_medals: awardedMedals,
        total_new_medals: awardedMedals.length
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('Error awarding medals:', error)
    
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        details: error.message 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})

async function awardXP(supabaseClient: any, userId: string, xpAmount: number) {
  try {
    // Get current user data
    const { data: user, error: userError } = await supabaseClient
      .from('users')
      .select('level, xp')
      .eq('id', userId)
      .single()

    if (userError || !user) {
      console.error('Error fetching user for XP award:', userError)
      return
    }

    const newXP = user.xp + xpAmount
    let newLevel = user.level

    // Check for level up (simple formula: level * 100 XP needed)
    while (newXP >= newLevel * 100 && newLevel < 4) {
      newLevel++
    }

    // Update user
    const { error: updateError } = await supabaseClient
      .from('users')
      .update({ 
        xp: newXP,
        level: newLevel
      })
      .eq('id', userId)

    if (updateError) {
      console.error('Error updating user XP/level:', updateError)
      return
    }

    // If level increased, check for new level medals
    if (newLevel > user.level) {
      console.log(`User ${userId} leveled up from ${user.level} to ${newLevel}`)
      
      // Recursively check for new medals after level up
      await supabaseClient.functions.invoke('award-medals', {
        body: { user_id: userId, event_type: 'level_up' }
      })
    }

  } catch (error) {
    console.error('Error in awardXP:', error)
  }
}
