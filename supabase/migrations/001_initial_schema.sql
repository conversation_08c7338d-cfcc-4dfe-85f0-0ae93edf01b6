-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table
CREATE TABLE users (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  level INTEGER DEFAULT 1 CHECK (level >= 1 AND level <= 4),
  xp INTEGER DEFAULT 0 CHECK (xp >= 0),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Stories table
CREATE TABLE stories (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  author_id UUID REFERENCES users(id) ON DELETE CASCADE,
  title TEXT NOT NULL CHECK (length(title) >= 5 AND length(title) <= 200),
  content TEXT NOT NULL CHECK (length(content) >= 20),
  section TEXT NOT NULL,
  tags TEXT[] DEFAULT '{}',
  camp_a_name TEXT NOT NULL CHECK (length(camp_a_name) >= 1 AND length(camp_a_name) <= 50),
  camp_b_name TEXT NOT NULL CHECK (length(camp_b_name) >= 1 AND length(camp_b_name) <= 50),
  heat INTEGER DEFAULT 0 CHECK (heat >= 0),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Votes table
CREATE TABLE votes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  story_id UUID REFERENCES stories(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  vote_type TEXT NOT NULL CHECK (vote_type IN ('camp', 'belief')),
  vote_value TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(story_id, user_id, vote_type)
);

-- Reactions table
CREATE TABLE reactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  story_id UUID REFERENCES stories(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  emoji_text TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(story_id, user_id, emoji_text)
);

-- Medals table
CREATE TABLE medals (
  id SERIAL PRIMARY KEY,
  name TEXT NOT NULL UNIQUE,
  description TEXT NOT NULL,
  icon_url TEXT NOT NULL,
  medal_type TEXT NOT NULL DEFAULT 'bronze' CHECK (medal_type IN ('bronze', 'silver', 'gold', 'diamond')),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- User medals table
CREATE TABLE user_medals (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  medal_id INTEGER REFERENCES medals(id) ON DELETE CASCADE,
  achieved_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id, medal_id)
);

-- User activity tracking for rate limiting
CREATE TABLE user_activity (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  activity_type TEXT NOT NULL CHECK (activity_type IN ('vote', 'post', 'reaction')),
  activity_date DATE DEFAULT CURRENT_DATE,
  count INTEGER DEFAULT 1,
  UNIQUE(user_id, activity_type, activity_date)
);

-- Indexes for performance
CREATE INDEX idx_stories_heat_created ON stories(heat DESC, created_at DESC);
CREATE INDEX idx_stories_author ON stories(author_id);
CREATE INDEX idx_stories_section ON stories(section);
CREATE INDEX idx_votes_story ON votes(story_id);
CREATE INDEX idx_votes_user ON votes(user_id);
CREATE INDEX idx_reactions_story ON reactions(story_id);
CREATE INDEX idx_reactions_user ON reactions(user_id);
CREATE INDEX idx_user_medals_user ON user_medals(user_id);
CREATE INDEX idx_user_activity_user_date ON user_activity(user_id, activity_date);

-- Row Level Security (RLS) policies
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE stories ENABLE ROW LEVEL SECURITY;
ALTER TABLE votes ENABLE ROW LEVEL SECURITY;
ALTER TABLE reactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_medals ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_activity ENABLE ROW LEVEL SECURITY;

-- Users policies
CREATE POLICY "Users can view all profiles" ON users FOR SELECT USING (true);
CREATE POLICY "Users can update own profile" ON users FOR UPDATE USING (auth.uid() = id);

-- Stories policies
CREATE POLICY "Anyone can view stories" ON stories FOR SELECT USING (true);
CREATE POLICY "Authenticated users can create stories" ON stories FOR INSERT WITH CHECK (auth.uid() = author_id);
CREATE POLICY "Authors can update own stories" ON stories FOR UPDATE USING (auth.uid() = author_id);

-- Votes policies
CREATE POLICY "Anyone can view votes" ON votes FOR SELECT USING (true);
CREATE POLICY "Authenticated users can vote" ON votes FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own votes" ON votes FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own votes" ON votes FOR DELETE USING (auth.uid() = user_id);

-- Reactions policies
CREATE POLICY "Anyone can view reactions" ON reactions FOR SELECT USING (true);
CREATE POLICY "Authenticated users can react" ON reactions FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can delete own reactions" ON reactions FOR DELETE USING (auth.uid() = user_id);

-- User medals policies
CREATE POLICY "Anyone can view user medals" ON user_medals FOR SELECT USING (true);
CREATE POLICY "System can award medals" ON user_medals FOR INSERT WITH CHECK (true);

-- User activity policies
CREATE POLICY "Users can view own activity" ON user_activity FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "System can track activity" ON user_activity FOR INSERT WITH CHECK (true);
CREATE POLICY "System can update activity" ON user_activity FOR UPDATE USING (true);
