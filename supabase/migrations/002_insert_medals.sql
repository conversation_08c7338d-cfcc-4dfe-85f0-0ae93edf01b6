-- Insert predefined medals based on product.md
INSERT INTO medals (name, description, icon_url, medal_type) VALUES
-- Level medals (等级勋章)
('勤劳的猹勋章', '达到LV2等级', 'assets/icons/hardworking_zha.png', 'bronze'),
('瓜地闰土勋章', '达到LV3等级', 'assets/icons/runtu.png', 'silver'),
('瓜田承包户勋章', '达到LV4等级', 'assets/icons/contractor.png', 'gold'),

-- Creation medals (创作勋章)
('第一口瓜', '发布了第一个故事', 'assets/icons/first_melon.png', 'bronze'),
('故事新秀', '发布10个故事', 'assets/icons/story_rookie.png', 'silver'),
('高产瓜主', '发布50个故事', 'assets/icons/productive_author.png', 'gold'),

-- Heat medals (热度勋章)
('热瓜认证', '单个故事热度达到100', 'assets/icons/hot_certified.png', 'bronze'),
('爆款制造机', '单个故事热度达到500', 'assets/icons/viral_maker.png', 'silver'),
('瓜神本神', '单个故事热度达到1000', 'assets/icons/melon_god.png', 'diamond'),

-- Interaction medals (互动勋章)
('逻辑大师', '获得100个"逻辑满分"投票', 'assets/icons/logic_master.png', 'silver'),
('共鸣之王', '获得100个"我也有类似经历"投票', 'assets/icons/resonance_king.png', 'silver'),
('Drama之王', '故事获得超过200个投票', 'assets/icons/drama_king.png', 'gold'),

-- Community medals (社区勋章)
('开荒元老', '前100名注册用户', 'assets/icons/pioneer.png', 'gold'),
('吃瓜劳模', '投票超过100次', 'assets/icons/voting_hero.png', 'bronze'),
('瓜田常青树', '连续30天活跃', 'assets/icons/evergreen.png', 'silver'),

-- Special medals
('表情包大师', '使用了所有类型的emoji反应', 'assets/icons/emoji_master.png', 'silver'),
('瓜田新人', '注册账号', 'assets/icons/newcomer.png', 'bronze');

-- Create function to check if user qualifies for medals
CREATE OR REPLACE FUNCTION check_user_medals(user_id_param UUID)
RETURNS TABLE(medal_id INTEGER, medal_name TEXT) AS $$
DECLARE
    user_level INTEGER;
    user_stories_count INTEGER;
    user_votes_count INTEGER;
    user_max_heat INTEGER;
    user_registration_order INTEGER;
    user_active_days INTEGER;
    logic_votes INTEGER;
    resonance_votes INTEGER;
    drama_votes INTEGER;
    emoji_types_used INTEGER;
BEGIN
    -- Get user stats
    SELECT level INTO user_level FROM users WHERE id = user_id_param;
    
    SELECT COUNT(*) INTO user_stories_count 
    FROM stories WHERE author_id = user_id_param;
    
    SELECT COUNT(*) INTO user_votes_count 
    FROM votes WHERE user_id = user_id_param;
    
    SELECT COALESCE(MAX(heat), 0) INTO user_max_heat 
    FROM stories WHERE author_id = user_id_param;
    
    -- Check registration order (simplified)
    SELECT COUNT(*) + 1 INTO user_registration_order 
    FROM users WHERE created_at < (SELECT created_at FROM users WHERE id = user_id_param);
    
    -- Check active days (simplified - count distinct days with activity)
    SELECT COUNT(DISTINCT activity_date) INTO user_active_days 
    FROM user_activity WHERE user_id = user_id_param;
    
    -- Check specific vote types
    SELECT COUNT(*) INTO logic_votes 
    FROM votes v JOIN stories s ON v.story_id = s.id 
    WHERE s.author_id = user_id_param AND v.vote_value = 'believe' AND v.vote_type = 'belief';
    
    SELECT COUNT(*) INTO resonance_votes 
    FROM votes v JOIN stories s ON v.story_id = s.id 
    WHERE s.author_id = user_id_param AND v.vote_value = 'believe' AND v.vote_type = 'belief';
    
    SELECT COUNT(*) INTO drama_votes 
    FROM votes v JOIN stories s ON v.story_id = s.id 
    WHERE s.author_id = user_id_param;
    
    -- Check emoji variety
    SELECT COUNT(DISTINCT emoji_text) INTO emoji_types_used 
    FROM reactions WHERE user_id = user_id_param;
    
    -- Return qualifying medals
    RETURN QUERY
    SELECT m.id, m.name FROM medals m WHERE
        (m.name = '瓜田新人') OR
        (m.name = '勤劳的猹勋章' AND user_level >= 2) OR
        (m.name = '瓜地闰土勋章' AND user_level >= 3) OR
        (m.name = '瓜田承包户勋章' AND user_level >= 4) OR
        (m.name = '第一口瓜' AND user_stories_count >= 1) OR
        (m.name = '故事新秀' AND user_stories_count >= 10) OR
        (m.name = '高产瓜主' AND user_stories_count >= 50) OR
        (m.name = '热瓜认证' AND user_max_heat >= 100) OR
        (m.name = '爆款制造机' AND user_max_heat >= 500) OR
        (m.name = '瓜神本神' AND user_max_heat >= 1000) OR
        (m.name = '吃瓜劳模' AND user_votes_count >= 100) OR
        (m.name = '开荒元老' AND user_registration_order <= 100) OR
        (m.name = '瓜田常青树' AND user_active_days >= 30) OR
        (m.name = '逻辑大师' AND logic_votes >= 100) OR
        (m.name = '共鸣之王' AND resonance_votes >= 100) OR
        (m.name = 'Drama之王' AND drama_votes >= 200) OR
        (m.name = '表情包大师' AND emoji_types_used >= 10);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
