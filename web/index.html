<!DOCTYPE html>
<html>
<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="瓜田 - 分享故事，站队吃瓜。一个有趣的故事分享和投票平台。">
  <meta name="keywords" content="瓜田,故事分享,投票,吃瓜,社交平台">
  <meta name="author" content="Guatian Team">

  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://guatian.app/">
  <meta property="og:title" content="瓜田 - 分享故事，站队吃瓜">
  <meta property="og:description" content="一个有趣的故事分享和投票平台">
  <meta property="og:image" content="https://guatian.app/icons/Icon-512.png">

  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image">
  <meta property="twitter:url" content="https://guatian.app/">
  <meta property="twitter:title" content="瓜田 - 分享故事，站队吃瓜">
  <meta property="twitter:description" content="一个有趣的故事分享和投票平台">
  <meta property="twitter:image" content="https://guatian.app/icons/Icon-512.png">

  <!-- iOS meta tags & icons -->
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
  <meta name="apple-mobile-web-app-title" content="瓜田">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>
  <link rel="icon" type="image/svg+xml" href="favicon.svg"/>

  <title>瓜田 - 分享故事，站队吃瓜</title>
  <link rel="manifest" href="manifest.json">
</head>
<body>
  <script src="flutter_bootstrap.js" async></script>
</body>
</html>
