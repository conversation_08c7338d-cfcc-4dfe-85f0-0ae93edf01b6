#!/bin/bash

echo "🍉 Setting up Guatian project..."

# Check if Flutter is installed
if ! command -v flutter &> /dev/null; then
    echo "❌ Flutter is not installed. Please install Flutter first."
    exit 1
fi

echo "✅ Flutter found"

# Check Flutter version
flutter --version

# Install dependencies
echo "📦 Installing dependencies..."
flutter pub get

# Generate model files
echo "🔧 Generating model files..."
flutter packages pub run build_runner build --delete-conflicting-outputs

# Create asset directories
echo "📁 Creating asset directories..."
mkdir -p assets/images
mkdir -p assets/animations
mkdir -p assets/icons
mkdir -p assets/fonts

# Run tests
echo "🧪 Running tests..."
flutter test

# Check for any issues
echo "🔍 Running flutter analyze..."
flutter analyze

echo "📝 Please complete the following setup steps:"
echo ""
echo "1. Update your .env file with your Supabase credentials:"
echo "   - SUPABASE_URL=https://rrxsdllelhdnbatmvact.supabase.co"
echo "   - SUPABASE_ANON_KEY=your_anon_key_here"
echo ""
echo "2. Run the SQL migrations in your Supabase project:"
echo "   - Execute supabase/migrations/001_initial_schema.sql"
echo "   - Execute supabase/migrations/002_insert_medals.sql"
echo ""
echo "3. Deploy the Edge Functions:"
echo "   - Deploy supabase/functions/calculate-heat/"
echo "   - Deploy supabase/functions/award-medals/"
echo ""
echo "4. Add font files to assets/fonts/:"
echo "   - Montserrat-Bold.ttf"
echo "   - Montserrat-ExtraBold.ttf"
echo "   - NotoSansSC-Regular.ttf"
echo "   - NotoSansSC-Medium.ttf"
echo "   - NotoSansSC-Bold.ttf"
echo ""
echo "5. Add medal icons to assets/icons/"
echo ""
echo "🚀 After completing these steps, run: flutter run"
echo ""
echo "🍉 Happy coding!"
