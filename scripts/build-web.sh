#!/bin/bash

echo "🍉 Building Guatian for Web..."

# Clean previous builds
echo "🧹 Cleaning previous builds..."
flutter clean

# Get dependencies
echo "📦 Installing dependencies..."
flutter pub get

# Generate code if needed
echo "🔧 Generating code..."
flutter packages pub run build_runner build --delete-conflicting-outputs

# Run tests
echo "🧪 Running tests..."
flutter test

# Build for web
echo "🌐 Building for web..."
flutter build web --release --web-renderer canvaskit --base-href /

# Optimize build
echo "⚡ Optimizing build..."
# Remove source maps in production
find build/web -name "*.js.map" -delete
find build/web -name "*.dart.js.map" -delete

echo "✅ Web build completed!"
echo "📁 Build output: build/web/"
echo "🚀 Ready for deployment!"

# Display build size
echo "📊 Build size:"
du -sh build/web/
